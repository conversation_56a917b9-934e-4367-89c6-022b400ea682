<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Booking System - Documentation Presentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35, #F7931E, #FFD700);
            color: #2C3E50;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .presentation-header {
            background: rgba(44, 62, 80, 0.95);
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #FF6B35, #FFD700);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .slide-counter {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* Main Content */
        .slides-container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 40px 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        /* Slide Content */
        .slide h1 {
            font-size: 3rem;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
        }

        .slide h2 {
            font-size: 2.5rem;
            margin-bottom: 25px;
            color: #2C3E50;
            border-bottom: 3px solid #FF6B35;
            padding-bottom: 10px;
        }

        .slide h3 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: #34495E;
        }

        .slide p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 20px;
            text-align: justify;
        }

        .slide ul {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-left: 30px;
        }

        .slide li {
            margin-bottom: 10px;
            position: relative;
        }

        .slide li::before {
            content: "🌍";
            position: absolute;
            left: -25px;
        }

        /* Special Slide Types */
        .title-slide {
            text-align: center;
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 215, 0, 0.1));
        }

        .title-slide h1 {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .title-slide .subtitle {
            font-size: 1.5rem;
            color: #7F8C8D;
            margin-bottom: 40px;
        }

        .title-slide .author {
            font-size: 1.2rem;
            color: #34495E;
            margin-top: 40px;
        }

        /* Code Slides */
        .code-slide pre {
            background: #2C3E50;
            color: #ECF0F1;
            padding: 25px;
            border-radius: 10px;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 20px 0;
            border-left: 5px solid #FF6B35;
        }

        .code-slide code {
            font-family: 'Courier New', monospace;
        }

        /* Stats Slides */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid rgba(255, 107, 53, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #FF6B35;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            color: #34495E;
            margin-top: 10px;
        }

        /* Navigation */
        .navigation {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(44, 62, 80, 0.8);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 107, 53, 0.9);
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Slide Indicators */
        .slide-indicators {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: #FF6B35;
            transform: scale(1.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .slide {
                padding: 20px 30px;
            }

            .slide h1 {
                font-size: 2.5rem;
            }

            .slide h2 {
                font-size: 2rem;
            }

            .slide p, .slide li {
                font-size: 1rem;
            }

            .presentation-header {
                padding: 10px 20px;
            }

            .logo {
                font-size: 1.2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* African Pattern Overlay */
        .african-pattern {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(247, 147, 30, 0.05) 0%, transparent 50%);
            background-size: 100px 100px;
            pointer-events: none;
            z-index: -1;
        }

        /* Progress Bar */
        .progress-bar {
            position: absolute;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #FF6B35, #F7931E, #FFD700);
            transition: width 0.3s ease;
            z-index: 1001;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="progress-bar" id="progressBar"></div>
        
        <header class="presentation-header">
            <div class="logo">🌍 EventBooking System</div>
            <div class="slide-counter">
                <span id="currentSlide">1</span> / <span id="totalSlides">15</span>
            </div>
        </header>

        <div class="slides-container">
            <div class="african-pattern"></div>
            
            <!-- Slide 1: Title -->
            <div class="slide active title-slide">
                <h1>📚 Event Booking System</h1>
                <p class="subtitle">Comprehensive Documentation Presentation</p>
                <p class="subtitle">🌍 Celebrating African Culture Through Technology</p>
                <div class="author">
                    <p><strong>Developed by:</strong> Larry Nkumbe</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +237 652 731 798</p>
                    <p><strong>Live Platform:</strong> https://eventbooking-z8ip.onrender.com</p>
                </div>
            </div>

            <!-- Slide 2: Project Overview -->
            <div class="slide">
                <h2>🎯 Project Overview</h2>
                <h3>Vision Statement</h3>
                <p>To become the leading digital platform for event discovery and booking in Central Africa, celebrating and promoting African culture while providing world-class user experience.</p>
                
                <h3>Mission Statement</h3>
                <p>Empowering event organizers and attendees across Cameroon and Central Africa with a professional, accessible, and culturally-authentic platform that simplifies event management and enhances community engagement through technology.</p>
                
                <h3>Key Features</h3>
                <ul>
                    <li>🔐 Secure User Management System</li>
                    <li>🎫 Advanced Event Discovery & Search</li>
                    <li>🛒 Intelligent Shopping Cart & Booking</li>
                    <li>🛡️ Comprehensive Admin Panel</li>
                    <li>📱 Mobile-First Responsive Design</li>
                    <li>☁️ Cloud-Native Infrastructure</li>
                </ul>
            </div>

            <!-- Slide 3: Target Audience -->
            <div class="slide">
                <h2>👥 Target Audience Analysis</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">70%</span>
                        <div class="stat-label">Event Attendees<br>Ages 18-45</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">25%</span>
                        <div class="stat-label">Event Organizers<br>Businesses & Organizations</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">5%</span>
                        <div class="stat-label">System Administrators<br>Technical Staff</div>
                    </div>
                </div>

                <h3>Primary User Personas</h3>
                <ul>
                    <li><strong>Young Professional (25-35):</strong> Urban professionals seeking networking and entertainment</li>
                    <li><strong>University Student (18-25):</strong> Budget-conscious, socially active, mobile-first users</li>
                    <li><strong>Cultural Enthusiast (30-45):</strong> Values authentic African representation and quality experiences</li>
                </ul>
            </div>

            <!-- Slide 4: System Architecture -->
            <div class="slide">
                <h2>🏗️ System Architecture</h2>
                
                <h3>Technology Stack</h3>
                <ul>
                    <li><strong>Frontend:</strong> HTML5, CSS3, JavaScript, Bootstrap 5</li>
                    <li><strong>Backend:</strong> PHP 8.2, Apache 2.4, PDO</li>
                    <li><strong>Database:</strong> PostgreSQL 14 (Production), MySQL 8.0 (Development)</li>
                    <li><strong>Infrastructure:</strong> Docker, Render Cloud Platform</li>
                </ul>

                <h3>Architecture Pattern: MVC</h3>
                <ul>
                    <li><strong>Model:</strong> Database entities, business rules, data validation</li>
                    <li><strong>View:</strong> HTML templates, CSS styling, responsive layouts</li>
                    <li><strong>Controller:</strong> PHP scripts, request routing, session management</li>
                </ul>

                <h3>Core Modules</h3>
                <ul>
                    <li>🔐 Authentication Module</li>
                    <li>🎫 Event Management Module</li>
                    <li>🛒 Booking Module</li>
                    <li>🛡️ Admin Module</li>
                    <li>📸 Image Management Module</li>
                </ul>
            </div>

            <!-- Slide 5: Design Philosophy -->
            <div class="slide">
                <h2>🎨 Design Philosophy</h2>
                
                <h3>"Cultural Authenticity meets Modern Excellence"</h3>
                
                <h3>Core Design Principles</h3>
                <ul>
                    <li><strong>🌍 Cultural Authenticity:</strong> African-centric visual language and local context</li>
                    <li><strong>🎯 User-Centered Design:</strong> Mobile-first approach with accessibility focus</li>
                    <li><strong>💎 Professional Excellence:</strong> Modern web standards and quality assurance</li>
                </ul>

                <h3>African Color Psychology</h3>
                <ul>
                    <li><strong>Orange (#FF6B35):</strong> Creativity, Energy, Warmth</li>
                    <li><strong>Gold (#F7931E):</strong> Wealth, Wisdom, Sun</li>
                    <li><strong>Green (#228B22):</strong> Growth, Harmony, Nature</li>
                    <li><strong>Red (#DC143C):</strong> Strength, Passion, Life Force</li>
                </ul>

                <h3>Cultural Elements</h3>
                <ul>
                    <li>Kente-inspired geometric patterns</li>
                    <li>Mudcloth-inspired organic textures</li>
                    <li>Adinkra symbol integration</li>
                    <li>Traditional African iconography</li>
                </ul>
            </div>

            <!-- Slide 6: Database Design -->
            <div class="slide code-slide">
                <h2>🗄️ Database Design</h2>

                <h3>PostgreSQL Schema with African Context</h3>
                <pre><code>-- Users Table with African Context
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    preferred_language VARCHAR(10) DEFAULT 'en',
    city VARCHAR(50),
    country VARCHAR(50) DEFAULT 'Cameroon',
    interests TEXT[], -- PostgreSQL array
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Events Table with African Categories
CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL, -- music, sports, culture
    date DATE NOT NULL,
    venue VARCHAR(200) NOT NULL,
    city VARCHAR(50) NOT NULL,
    country VARCHAR(50) DEFAULT 'Cameroon',
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'XAF', -- CFA franc
    total_tickets INTEGER NOT NULL,
    available_tickets INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'active'
);</code></pre>
            </div>

            <!-- Slide 7: Security Implementation -->
            <div class="slide code-slide">
                <h2>🔒 Security Implementation</h2>

                <h3>Authentication & Password Security</h3>
                <pre><code>// Secure password hashing with Argon2ID
$hashedPassword = password_hash($password, PASSWORD_ARGON2ID, [
    'memory_cost' => 65536, // 64 MB
    'time_cost' => 4,       // 4 iterations
    'threads' => 3          // 3 threads
]);

// Input validation and sanitization
function sanitizeInput($input) {
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    return $input;
}

// CSRF Protection
function generateCSRFToken() {
    return bin2hex(random_bytes(32));
}

// Rate limiting for login attempts
private const MAX_LOGIN_ATTEMPTS = 5;
private const LOCKOUT_DURATION = 900; // 15 minutes</code></pre>

                <h3>Security Features</h3>
                <ul>
                    <li>Argon2ID password hashing</li>
                    <li>CSRF token protection</li>
                    <li>Rate limiting and account lockout</li>
                    <li>Input validation and sanitization</li>
                    <li>SQL injection prevention</li>
                </ul>
            </div>

            <!-- Slide 8: African Image System -->
            <div class="slide">
                <h2>📸 African Image System</h2>

                <h3>Authentic African Representation</h3>
                <p>Custom image mapping system that automatically assigns culturally appropriate African imagery to events based on their type and content.</p>

                <h3>Image Categories</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">🎵</span>
                        <div class="stat-label">Music & Entertainment<br>Afrobeats, Makossa, Jazz</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">⚽</span>
                        <div class="stat-label">Sports Events<br>Football, Boxing, Athletics</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">🎭</span>
                        <div class="stat-label">Cultural Events<br>Traditional, Heritage, Art</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">💼</span>
                        <div class="stat-label">Business & Tech<br>Conferences, Startups</div>
                    </div>
                </div>

                <h3>Technical Features</h3>
                <ul>
                    <li>Smart image mapping based on event names</li>
                    <li>SVG graphics for scalability</li>
                    <li>Fallback system for reliability</li>
                    <li>Manual upload interface for custom images</li>
                </ul>
            </div>

            <!-- Slide 9: Responsive Design -->
            <div class="slide">
                <h2>📱 Responsive Design</h2>

                <h3>Mobile-First Strategy</h3>
                <p>Designed specifically for African mobile users with optimized touch interfaces and fast loading times.</p>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">70%</span>
                        <div class="stat-label">Mobile Traffic<br>Expected Usage</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">44px</span>
                        <div class="stat-label">Minimum Touch Target<br>Apple Standard</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">&lt;3s</span>
                        <div class="stat-label">Page Load Time<br>Performance Target</div>
                    </div>
                </div>

                <h3>Responsive Features</h3>
                <ul>
                    <li><strong>Breakpoints:</strong> 576px, 768px, 992px, 1200px</li>
                    <li><strong>Touch Optimization:</strong> Large buttons, gesture support</li>
                    <li><strong>Progressive Enhancement:</strong> Works without JavaScript</li>
                    <li><strong>Accessibility:</strong> WCAG 2.1 compliance</li>
                    <li><strong>Performance:</strong> Lazy loading, image optimization</li>
                </ul>
            </div>

            <!-- Slide 10: Performance Metrics -->
            <div class="slide">
                <h2>📊 Performance Metrics & KPIs</h2>

                <h3>User Engagement Targets</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">10K+</span>
                        <div class="stat-label">Monthly Active Users<br>Year 1 Target</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">15%</span>
                        <div class="stat-label">Booking Conversion<br>View to Purchase</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">60%</span>
                        <div class="stat-label">User Retention<br>30-Day Return Rate</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">99.9%</span>
                        <div class="stat-label">System Uptime<br>Availability Target</div>
                    </div>
                </div>

                <h3>Business Performance</h3>
                <ul>
                    <li><strong>Revenue Growth:</strong> 20% month-over-month target</li>
                    <li><strong>Event Catalog:</strong> 100+ active events across categories</li>
                    <li><strong>Geographic Reach:</strong> 5+ major Cameroon cities</li>
                    <li><strong>Market Share:</strong> 30% of digital event bookings</li>
                </ul>
            </div>

            <!-- Slide 11: Development Methodology -->
            <div class="slide">
                <h2>⚡ Development Methodology</h2>

                <h3>Agile Development Process</h3>
                <p><strong>Sprint Planning → Development → Testing → Review → Deploy</strong></p>

                <h3>Development Phases</h3>
                <ul>
                    <li><strong>Phase 1: Foundation (Weeks 1-4)</strong>
                        <ul style="margin-left: 20px; margin-top: 10px;">
                            <li>✅ Project setup and database design</li>
                            <li>✅ Core authentication system</li>
                            <li>✅ Basic UI framework</li>
                        </ul>
                    </li>
                    <li><strong>Phase 2: Core Features (Weeks 5-8)</strong>
                        <ul style="margin-left: 20px; margin-top: 10px;">
                            <li>✅ Event management system</li>
                            <li>✅ Shopping cart and booking</li>
                            <li>✅ Search and filtering</li>
                        </ul>
                    </li>
                    <li><strong>Phase 3: Enhancement (Weeks 9-12)</strong>
                        <ul style="margin-left: 20px; margin-top: 10px;">
                            <li>✅ Admin panel development</li>
                            <li>✅ African image system</li>
                            <li>✅ Performance optimization</li>
                        </ul>
                    </li>
                    <li><strong>Phase 4: Polish & Deploy (Weeks 13-16)</strong>
                        <ul style="margin-left: 20px; margin-top: 10px;">
                            <li>✅ Security hardening</li>
                            <li>✅ Cross-browser testing</li>
                            <li>✅ Production deployment</li>
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- Slide 12: Competitive Advantages -->
            <div class="slide">
                <h2>🏆 Competitive Advantages</h2>

                <h3>🌍 Cultural Authenticity</h3>
                <ul>
                    <li><strong>African-Centric Design:</strong> Visual elements that resonate with local culture</li>
                    <li><strong>Local Context:</strong> Deep understanding of Central African event landscape</li>
                    <li><strong>Community Connection:</strong> Built by Africans for Africans</li>
                    <li><strong>Cultural Celebration:</strong> Platform that promotes African heritage</li>
                </ul>

                <h3>💻 Technical Excellence</h3>
                <ul>
                    <li><strong>Modern Architecture:</strong> Latest web technologies and best practices</li>
                    <li><strong>Cloud-Native:</strong> Scalable infrastructure on professional platform</li>
                    <li><strong>Security-First:</strong> Industry-standard security measures</li>
                    <li><strong>Performance Optimized:</strong> Fast loading and responsive experience</li>
                </ul>

                <h3>📈 Business Model Innovation</h3>
                <ul>
                    <li><strong>First-Mover Advantage:</strong> Early entry into underserved market</li>
                    <li><strong>Sustainable Revenue:</strong> Multiple revenue streams</li>
                    <li><strong>Partnership Ready:</strong> Integration with local payment providers</li>
                    <li><strong>Scalable Growth:</strong> Architecture supports rapid expansion</li>
                </ul>
            </div>

            <!-- Slide 13: Future Roadmap -->
            <div class="slide">
                <h2>🚀 Strategic Roadmap</h2>

                <h3>Phase 2: Enhancement (Months 4-9)</h3>
                <ul>
                    <li>🔄 Payment Integration (Mobile Money, Credit Cards)</li>
                    <li>🔄 Email Notification System</li>
                    <li>🔄 Multi-Language Support (French)</li>
                    <li>🔄 Advanced Analytics Dashboard</li>
                </ul>

                <h3>Phase 3: Expansion (Months 10-18)</h3>
                <ul>
                    <li>📅 Mobile Application Development (iOS/Android)</li>
                    <li>📅 Regional Expansion (Chad, CAR, Equatorial Guinea)</li>
                    <li>📅 Enterprise Features for Large Organizers</li>
                    <li>📅 Blockchain Ticketing System</li>
                </ul>

                <h3>Phase 4: Scale & Innovation (Months 19+)</h3>
                <ul>
                    <li>🚀 Continental Expansion (20+ African Countries)</li>
                    <li>🚀 AI-Powered Recommendations</li>
                    <li>🚀 Virtual Reality Event Previews</li>
                    <li>🚀 Cultural Preservation Digital Archive</li>
                </ul>

                <div class="stats-grid">
                    <div class="stat-card">
                        <span class="stat-number">50K+</span>
                        <div class="stat-label">Users by Month 18<br>Regional Expansion</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">$50K+</span>
                        <div class="stat-label">Monthly Revenue<br>Phase 3 Target</div>
                    </div>
                </div>
            </div>

            <!-- Slide 14: Cultural Impact -->
            <div class="slide">
                <h2>🌍 Cultural Impact & Social Responsibility</h2>

                <h3>🎭 Cultural Preservation & Promotion</h3>
                <ul>
                    <li><strong>Heritage Celebration:</strong> Special features for traditional events</li>
                    <li><strong>Language Preservation:</strong> Support for local languages</li>
                    <li><strong>Artist Promotion:</strong> Platform for emerging African artists</li>
                    <li><strong>Cultural Education:</strong> Information about African traditions</li>
                </ul>

                <h3>📚 Educational & Economic Impact</h3>
                <ul>
                    <li><strong>Knowledge Sharing:</strong> Event management training resources</li>
                    <li><strong>Digital Literacy:</strong> Contributing to digital adoption</li>
                    <li><strong>Job Creation:</strong> Direct and indirect employment</li>
                    <li><strong>Tourism Boost:</strong> Attracting visitors to African events</li>
                </ul>

                <h3>Community Building</h3>
                <ul>
                    <li>Local community focus and cultural exchange</li>
                    <li>Youth engagement and entrepreneurship support</li>
                    <li>Diaspora connection with homeland events</li>
                    <li>Supporting African creative industries</li>
                </ul>
            </div>

            <!-- Slide 15: Conclusion -->
            <div class="slide title-slide">
                <h1>🎉 Conclusion</h1>
                <p class="subtitle">A Celebration of African Innovation</p>

                <h3>Key Achievements</h3>
                <ul style="text-align: left; max-width: 800px; margin: 0 auto;">
                    <li><strong>🎨 Cultural Authenticity:</strong> First African-themed event booking platform</li>
                    <li><strong>💻 Technical Excellence:</strong> Modern architecture with cloud deployment</li>
                    <li><strong>🌍 Social Impact:</strong> Promoting African culture and community engagement</li>
                    <li><strong>📈 Business Viability:</strong> Sustainable model with clear growth path</li>
                </ul>

                <div class="stats-grid" style="margin-top: 40px;">
                    <div class="stat-card">
                        <span class="stat-number">✅</span>
                        <div class="stat-label">Production Ready<br>Live Platform</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">🌍</span>
                        <div class="stat-label">African Authentic<br>Cultural Design</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">🚀</span>
                        <div class="stat-label">Scalable Future<br>Growth Ready</div>
                    </div>
                </div>

                <p style="margin-top: 40px; font-size: 1.3rem; color: #FF6B35;">
                    <strong>The Event Booking System is not just a platform—it's a celebration of African culture,
                    a showcase of technical excellence, and a catalyst for community engagement across Central Africa.</strong>
                </p>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">← Previous</button>
            <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Next →</button>
        </div>

        <!-- Slide Indicators -->
        <div class="slide-indicators" id="slideIndicators"></div>
    </div>

    <script>
        class PresentationController {
            constructor() {
                this.currentSlide = 0;
                this.slides = document.querySelectorAll('.slide');
                this.totalSlides = this.slides.length;
                this.init();
            }

            init() {
                this.updateSlideCounter();
                this.createIndicators();
                this.updateProgress();
                this.setupKeyboardNavigation();
                this.updateNavigationButtons();
            }

            changeSlide(direction) {
                const newSlide = this.currentSlide + direction;

                if (newSlide >= 0 && newSlide < this.totalSlides) {
                    // Remove active class from current slide
                    this.slides[this.currentSlide].classList.remove('active');

                    // Add prev class for animation
                    if (direction > 0) {
                        this.slides[this.currentSlide].classList.add('prev');
                    }

                    // Update current slide
                    this.currentSlide = newSlide;

                    // Add active class to new slide
                    this.slides[this.currentSlide].classList.add('active');

                    // Remove prev class after animation
                    setTimeout(() => {
                        this.slides.forEach(slide => slide.classList.remove('prev'));
                    }, 600);

                    this.updateSlideCounter();
                    this.updateIndicators();
                    this.updateProgress();
                    this.updateNavigationButtons();
                    this.animateSlideContent();
                }
            }

            goToSlide(slideIndex) {
                if (slideIndex >= 0 && slideIndex < this.totalSlides && slideIndex !== this.currentSlide) {
                    const direction = slideIndex > this.currentSlide ? 1 : -1;
                    this.currentSlide = slideIndex - direction;
                    this.changeSlide(direction);
                }
            }

            updateSlideCounter() {
                document.getElementById('currentSlide').textContent = this.currentSlide + 1;
                document.getElementById('totalSlides').textContent = this.totalSlides;
            }

            createIndicators() {
                const indicatorsContainer = document.getElementById('slideIndicators');
                indicatorsContainer.innerHTML = '';

                for (let i = 0; i < this.totalSlides; i++) {
                    const indicator = document.createElement('div');
                    indicator.className = 'indicator';
                    if (i === this.currentSlide) {
                        indicator.classList.add('active');
                    }
                    indicator.addEventListener('click', () => this.goToSlide(i));
                    indicatorsContainer.appendChild(indicator);
                }
            }

            updateIndicators() {
                const indicators = document.querySelectorAll('.indicator');
                indicators.forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === this.currentSlide);
                });
            }

            updateProgress() {
                const progress = ((this.currentSlide + 1) / this.totalSlides) * 100;
                document.getElementById('progressBar').style.width = progress + '%';
            }

            updateNavigationButtons() {
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                prevBtn.disabled = this.currentSlide === 0;
                nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
            }

            setupKeyboardNavigation() {
                document.addEventListener('keydown', (e) => {
                    switch(e.key) {
                        case 'ArrowLeft':
                        case 'ArrowUp':
                            e.preventDefault();
                            this.changeSlide(-1);
                            break;
                        case 'ArrowRight':
                        case 'ArrowDown':
                        case ' ':
                            e.preventDefault();
                            this.changeSlide(1);
                            break;
                        case 'Home':
                            e.preventDefault();
                            this.goToSlide(0);
                            break;
                        case 'End':
                            e.preventDefault();
                            this.goToSlide(this.totalSlides - 1);
                            break;
                    }
                });
            }

            animateSlideContent() {
                const currentSlideElement = this.slides[this.currentSlide];
                const elements = currentSlideElement.querySelectorAll('h1, h2, h3, p, ul, .stat-card, pre');

                elements.forEach((element, index) => {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';

                    setTimeout(() => {
                        element.style.transition = 'all 0.6s ease';
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }

            // Auto-play functionality (optional)
            startAutoPlay(interval = 10000) {
                this.autoPlayInterval = setInterval(() => {
                    if (this.currentSlide < this.totalSlides - 1) {
                        this.changeSlide(1);
                    } else {
                        this.goToSlide(0); // Loop back to start
                    }
                }, interval);
            }

            stopAutoPlay() {
                if (this.autoPlayInterval) {
                    clearInterval(this.autoPlayInterval);
                }
            }
        }

        // Global functions for button clicks
        let presentation;

        function changeSlide(direction) {
            presentation.changeSlide(direction);
        }

        function goToSlide(index) {
            presentation.goToSlide(index);
        }

        // Initialize presentation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            presentation = new PresentationController();

            // Optional: Start auto-play (uncomment to enable)
            // presentation.startAutoPlay(15000); // 15 seconds per slide

            // Stop auto-play on user interaction
            document.addEventListener('click', () => {
                presentation.stopAutoPlay();
            });

            document.addEventListener('keydown', () => {
                presentation.stopAutoPlay();
            });
        });

        // Touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;

        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });

        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe left - next slide
                    presentation.changeSlide(1);
                } else {
                    // Swipe right - previous slide
                    presentation.changeSlide(-1);
                }
            }
        }

        // Fullscreen support
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // Add fullscreen button (optional)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
    </script>
</body>
</html>
