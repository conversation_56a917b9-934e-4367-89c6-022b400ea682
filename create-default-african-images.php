<?php
/**
 * Create Default African Images
 * Creates placeholder African-themed images using CSS gradients
 * These can be replaced with real images later
 */

echo "<h1>🌍 Creating Default African Images</h1>";
echo "<p>Creating placeholder African-themed images for your events...</p>";

// Create directories
$directories = [
    'images/events/african',
    'images/events/african/music',
    'images/events/african/sports', 
    'images/events/african/culture',
    'images/events/african/business',
    'images/events/african/food',
    'images/events/african/fashion',
    'images/events/african/entertainment',
    'images/events/african/venues'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0777, true);
        echo "<p>✅ Created directory: <strong>$dir</strong></p>";
    }
}

// African color schemes
$africanColors = [
    'music' => ['#FF6B35', '#F7931E', '#FFD700'], // Orange, gold
    'sports' => ['#228B22', '#FFD700', '#DC143C'], // Green, gold, red
    'culture' => ['#8B4513', '#DAA520', '#CD853F'], // Brown, gold, tan
    'business' => ['#2F4F4F', '#708090', '#B0C4DE'], // Professional blues/grays
    'food' => ['#FF4500', '#FF8C00', '#FFA500'], // Orange tones
    'fashion' => ['#9932CC', '#BA55D3', '#DDA0DD'], // Purple tones
    'entertainment' => ['#FF1493', '#FF69B4', '#FFB6C1'], // Pink tones
    'venues' => ['#4682B4', '#5F9EA0', '#87CEEB'] // Blue tones
];

// Create SVG placeholder images
function createAfricanSVG($category, $title, $colors) {
    $gradient = implode(',', $colors);
    
    $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:' . $colors[0] . ';stop-opacity:1" />
      <stop offset="50%" style="stop-color:' . $colors[1] . ';stop-opacity:1" />
      <stop offset="100%" style="stop-color:' . $colors[2] . ';stop-opacity:1" />
    </linearGradient>
    <pattern id="pattern1" patternUnits="userSpaceOnUse" width="40" height="40">
      <circle cx="20" cy="20" r="3" fill="rgba(255,255,255,0.1)"/>
    </pattern>
  </defs>
  
  <rect width="100%" height="100%" fill="url(#grad1)"/>
  <rect width="100%" height="100%" fill="url(#pattern1)"/>
  
  <!-- African-inspired geometric patterns -->
  <g opacity="0.2">
    <polygon points="100,100 200,50 300,100 200,150" fill="white"/>
    <polygon points="500,200 600,150 700,200 600,250" fill="white"/>
    <polygon points="200,400 300,350 400,400 300,450" fill="white"/>
    <polygon points="600,450 700,400 800,450 700,500" fill="white"/>
  </g>
  
  <!-- Title -->
  <text x="400" y="300" font-family="Arial, sans-serif" font-size="48" font-weight="bold" 
        text-anchor="middle" fill="white" opacity="0.9">
    ' . strtoupper($title) . '
  </text>
  
  <!-- Subtitle -->
  <text x="400" y="350" font-family="Arial, sans-serif" font-size="24" 
        text-anchor="middle" fill="white" opacity="0.7">
    African ' . ucfirst($category) . ' Event
  </text>
  
  <!-- African pattern border -->
  <rect x="20" y="20" width="760" height="560" fill="none" 
        stroke="rgba(255,255,255,0.3)" stroke-width="4" stroke-dasharray="10,5"/>
</svg>';
    
    return $svg;
}

// Create placeholder images for each category
$imageDefinitions = [
    'music' => [
        'african_afrobeats_festival.svg' => 'AFROBEATS FESTIVAL',
        'african_makossa_concert.svg' => 'MAKOSSA CONCERT',
        'african_jazz_night.svg' => 'JAZZ NIGHT',
        'african_traditional_drums.svg' => 'TRADITIONAL MUSIC',
        'african_music_festival.svg' => 'MUSIC FESTIVAL',
        'african_concert_stage.svg' => 'CONCERT'
    ],
    'sports' => [
        'african_football_stadium.svg' => 'FOOTBALL MATCH',
        'african_boxing_match.svg' => 'BOXING CHAMPIONSHIP',
        'african_athletics_track.svg' => 'ATHLETICS',
        'african_handball_court.svg' => 'HANDBALL',
        'african_basketball_arena.svg' => 'BASKETBALL'
    ],
    'culture' => [
        'african_cultural_dance.svg' => 'CULTURAL DANCE',
        'african_traditional_ceremony.svg' => 'TRADITIONAL CEREMONY',
        'african_art_gallery.svg' => 'ART EXHIBITION',
        'african_heritage_site.svg' => 'HERITAGE FESTIVAL',
        'african_traditional_dance.svg' => 'TRADITIONAL DANCE'
    ],
    'business' => [
        'african_tech_summit.svg' => 'TECH SUMMIT',
        'african_business_conference.svg' => 'BUSINESS CONFERENCE',
        'african_startup_event.svg' => 'STARTUP EVENT',
        'african_workshop.svg' => 'WORKSHOP',
        'african_summit.svg' => 'SUMMIT'
    ],
    'food' => [
        'african_food_market.svg' => 'FOOD MARKET',
        'african_wine_event.svg' => 'WINE TASTING',
        'african_cooking_class.svg' => 'COOKING CLASS',
        'african_restaurant.svg' => 'RESTAURANT EVENT'
    ],
    'fashion' => [
        'african_fashion_runway.svg' => 'FASHION RUNWAY',
        'african_fashion_week.svg' => 'FASHION WEEK',
        'african_style_showcase.svg' => 'STYLE SHOWCASE'
    ],
    'entertainment' => [
        'african_comedy_show.svg' => 'COMEDY SHOW',
        'african_theater.svg' => 'THEATER',
        'african_gaming_tournament.svg' => 'GAMING TOURNAMENT',
        'african_party_event.svg' => 'PARTY EVENT'
    ],
    'venues' => [
        'yaunde_venue.svg' => 'YAOUNDÉ VENUE',
        'douala_venue.svg' => 'DOUALA VENUE',
        'palais_des_sports.svg' => 'PALAIS DES SPORTS',
        'omnisport_stadium.svg' => 'OMNISPORT STADIUM',
        'hilton_yaunde.svg' => 'HILTON YAOUNDÉ'
    ]
];

echo "<h2>🎨 Creating African-Themed Placeholder Images</h2>";

foreach ($imageDefinitions as $category => $images) {
    $colors = $africanColors[$category];
    
    foreach ($images as $filename => $title) {
        $svg = createAfricanSVG($category, $title, $colors);
        $filepath = "images/events/african/$category/$filename";
        
        if (file_put_contents($filepath, $svg)) {
            echo "<p>✅ Created: <strong>$filepath</strong></p>";
        } else {
            echo "<p>❌ Failed to create: <strong>$filepath</strong></p>";
        }
    }
}

// Create default African image
$defaultSvg = createAfricanSVG('general', 'AFRICAN EVENT', ['#FF6B35', '#F7931E', '#FFD700']);
file_put_contents('images/events/african/african_event_default.svg', $defaultSvg);
echo "<p>✅ Created default African image</p>";

echo "<h2>🔄 Converting SVG to JPG (Optional)</h2>";
echo "<p>The SVG images work great, but you can convert them to JPG if needed:</p>";
echo "<ol>";
echo "<li>Open each SVG file in a browser</li>";
echo "<li>Take a screenshot or use browser developer tools</li>";
echo "<li>Save as JPG with same filename</li>";
echo "<li>Replace the SVG files with JPG versions</li>";
echo "</ol>";

echo "<h2>🎉 African Images Created!</h2>";
echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ Success!</h3>";
echo "<p><strong>Created African-themed placeholder images for all event categories:</strong></p>";
echo "<ul>";
echo "<li>🎵 <strong>Music Events:</strong> Afrobeats, Makossa, Jazz, Traditional</li>";
echo "<li>⚽ <strong>Sports Events:</strong> Football, Boxing, Athletics</li>";
echo "<li>🎭 <strong>Cultural Events:</strong> Dances, Ceremonies, Art</li>";
echo "<li>💼 <strong>Business Events:</strong> Tech, Conferences, Startups</li>";
echo "<li>🍽️ <strong>Food Events:</strong> Markets, Wine, Cooking</li>";
echo "<li>👗 <strong>Fashion Events:</strong> Runway, Fashion Week</li>";
echo "<li>🎪 <strong>Entertainment:</strong> Comedy, Theater, Gaming</li>";
echo "<li>🏢 <strong>Venues:</strong> Yaoundé, Douala locations</li>";
echo "</ul>";
echo "<p><strong>These placeholder images use authentic African colors and patterns. You can replace them with real photos later!</strong></p>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;'>🏠 View Homepage</a>";
echo "<a href='events.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;'>🎫 Browse Events</a>";
echo "<a href='setup-african-images.php' style='background: #6f42c1; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 10px;'>📸 Upload Real Images</a>";
echo "</div>";
?>
