User Login: http://localhost:8080/login.php
Admin Login: http://localhost:8080/admin/index.php
http://localhost:8081/index.php?route=/database/structure&db=event_booking_system







Homepage Test
Visit: https://eventbooking-z8ip.onrender.com
Expected: Beautiful homepage with events displayed

2. 👤 User Registration Test
Visit: https://eventbooking-z8ip.onrender.com/register.php
Try: Create your own user account

3. 🔐 User Login Test
Visit: https://eventbooking-z8ip.onrender.com/login.php
Try: Login with your new account

4. 🎫 Events Page Test
Visit: https://eventbooking-z8ip.onrender.com/events.php
Expected: See the "Welcome Concert 2025" sample event

5. 🛡️ Admin Panel Test
Visit: https://eventbooking-z8ip.onrender.com/admin
Login with: