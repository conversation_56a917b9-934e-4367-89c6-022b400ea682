<?php
/**
 * Add More Exciting Events
 * Adds additional thrilling events to make the system more comprehensive
 */

require_once 'config/database.php';

echo "<h1>🎪 Adding More Exciting Events</h1>";
echo "<p>Adding concerts, festivals, sports tournaments, and unique entertainment events!</p>";

try {
    $pdo = getDBConnection();
    
    // Additional exciting events
    $moreEvents = [
        [
            'name' => 'Afrobeats Concert Spectacular',
            'description' => 'Mega Afrobeats concert featuring top artists from Nigeria, Ghana, and Cameroon. Live performances, DJ sets, and an unforgettable night of African music.',
            'date' => '2025-08-15',
            'time' => '20:00:00',
            'venue' => 'Palais des Congrès',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Afrobeats Entertainment',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/music-festival.jpg',
            'price' => 20000,
            'total_tickets' => 8000,
            'available_tickets' => 8000
        ],
        [
            'name' => 'Cameroon Tennis Open 2025',
            'description' => 'International tennis tournament featuring professional players from around the world. Watch world-class tennis in beautiful Cameroon.',
            'date' => '2025-09-20',
            'time' => '09:00:00',
            'venue' => 'Tennis Club de Yaoundé',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Tennis Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/sports/basketball-arena.jpg',
            'price' => 15000,
            'total_tickets' => 2000,
            'available_tickets' => 2000
        ],
        [
            'name' => 'Magic & Illusion Show',
            'description' => 'Mind-blowing magic show featuring international magicians and illusionists. Prepare to be amazed by incredible tricks and spectacular illusions.',
            'date' => '2025-07-10',
            'time' => '19:30:00',
            'venue' => 'Théâtre National',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Magic Entertainment International',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/sports/comedy-show.jpg',
            'price' => 12000,
            'total_tickets' => 1500,
            'available_tickets' => 1500
        ],
        [
            'name' => 'Cameroon Volleyball Championship',
            'description' => 'National volleyball championship with teams competing for the title. Fast-paced action, skilled athletes, and exciting matches.',
            'date' => '2025-10-15',
            'time' => '15:00:00',
            'venue' => 'Sports Complex Douala',
            'location' => 'Douala, Cameroon',
            'organizer' => 'Cameroon Volleyball Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/sports/handball-court.jpg',
            'price' => 6000,
            'total_tickets' => 3000,
            'available_tickets' => 3000
        ],
        [
            'name' => 'International Film Festival Cameroon',
            'description' => 'Prestigious film festival showcasing the best of African and international cinema. Red carpet events, premieres, and award ceremonies.',
            'date' => '2025-11-05',
            'time' => '18:00:00',
            'venue' => 'Cinema Le Capitole',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Film Institute',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/art-exhibition.jpg',
            'price' => 18000,
            'total_tickets' => 800,
            'available_tickets' => 800
        ],
        [
            'name' => 'Street Food Festival Cameroon',
            'description' => 'Celebration of Cameroon\'s diverse street food culture. Taste authentic dishes, cooking demonstrations, and food competitions.',
            'date' => '2025-06-15',
            'time' => '11:00:00',
            'venue' => 'Central Market Square',
            'location' => 'Douala, Cameroon',
            'organizer' => 'Cameroon Culinary Association',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/food-wine.jpg',
            'price' => 3000,
            'total_tickets' => 5000,
            'available_tickets' => 5000
        ],
        [
            'name' => 'Cameroon Marathon 2025',
            'description' => 'Annual marathon through the beautiful streets of Yaoundé. Full marathon, half marathon, and 10K races for all fitness levels.',
            'date' => '2025-12-01',
            'time' => '06:00:00',
            'venue' => 'Unity Palace Starting Point',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Athletics Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/adventure-outdoor.jpg',
            'price' => 5000,
            'total_tickets' => 10000,
            'available_tickets' => 10000
        ],
        [
            'name' => 'Jazz Night Under the Stars',
            'description' => 'Intimate jazz concert featuring local and international jazz musicians. Enjoy smooth jazz melodies under the beautiful Cameroon night sky.',
            'date' => '2025-07-25',
            'time' => '20:30:00',
            'venue' => 'Hilton Garden Terrace',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Jazz Society',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/music-festival.jpg',
            'price' => 15000,
            'total_tickets' => 500,
            'available_tickets' => 500
        ],
        [
            'name' => 'Cameroon Swimming Championships',
            'description' => 'National swimming competition featuring the country\'s best swimmers. Multiple events, age categories, and record-breaking performances.',
            'date' => '2025-08-30',
            'time' => '08:00:00',
            'venue' => 'Olympic Swimming Pool',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Swimming Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/sports/basketball-arena.jpg',
            'price' => 4000,
            'total_tickets' => 2000,
            'available_tickets' => 2000
        ],
        [
            'name' => 'Traditional Music & Dance Gala',
            'description' => 'Spectacular showcase of Cameroon\'s rich traditional music and dance heritage. Multiple ethnic groups, traditional instruments, and cultural performances.',
            'date' => '2025-09-05',
            'time' => '18:00:00',
            'venue' => 'National Museum Amphitheater',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Ministry of Arts and Culture',
            'organizer_contact' => '<EMAIL>',
            'image' => 'events/sports/dance-competition.jpg',
            'price' => 8000,
            'total_tickets' => 3000,
            'available_tickets' => 3000
        ]
    ];
    
    echo "<h2>🎭 Adding Diverse Entertainment Events</h2>";
    
    $successCount = 0;
    
    foreach ($moreEvents as $event) {
        // Check if event already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM events WHERE name = ?");
        $stmt->execute([$event['name']]);
        
        if ($stmt->fetchColumn() == 0) {
            // Insert new event
            $stmt = $pdo->prepare("
                INSERT INTO events (name, description, date, time, venue, location, organizer, organizer_contact, image, price, total_tickets, available_tickets, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            $stmt->execute([
                $event['name'],
                $event['description'],
                $event['date'],
                $event['time'],
                $event['venue'],
                $event['location'],
                $event['organizer'],
                $event['organizer_contact'],
                $event['image'],
                $event['price'],
                $event['total_tickets'],
                $event['available_tickets']
            ]);
            
            echo "<div style='margin: 10px 0; padding: 15px; border: 1px solid #17a2b8; border-radius: 8px; background: #d1ecf1;'>";
            echo "<h4 style='color: #0c5460; margin: 0 0 10px 0;'>✅ {$event['name']}</h4>";
            echo "<p style='margin: 5px 0;'><strong>Date:</strong> {$event['date']} at {$event['time']}</p>";
            echo "<p style='margin: 5px 0;'><strong>Venue:</strong> {$event['venue']}, {$event['location']}</p>";
            echo "<p style='margin: 5px 0;'><strong>Price:</strong> " . number_format($event['price']) . " CFA</p>";
            echo "<p style='margin: 5px 0;'><strong>Capacity:</strong> " . number_format($event['total_tickets']) . " tickets</p>";
            echo "</div>";
            
            $successCount++;
        } else {
            echo "<div style='margin: 10px 0; padding: 15px; border: 1px solid #ffc107; border-radius: 8px; background: #fff3cd;'>";
            echo "<h4 style='color: #856404; margin: 0;'>⚠️ {$event['name']} - Already exists</h4>";
            echo "</div>";
        }
    }
    
    // Get total events count
    $stmt = $pdo->query("SELECT COUNT(*) FROM events WHERE status = 'active'");
    $totalEvents = $stmt->fetchColumn();
    
    echo "<h2>🎊 Final Summary</h2>";
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0;'>";
    echo "<h3 style='margin: 0 0 15px 0;'>🏆 Event Booking System - Complete!</h3>";
    echo "<p><strong>New Events Added:</strong> $successCount</p>";
    echo "<p><strong>Total Active Events:</strong> $totalEvents</p>";
    echo "<p><strong>Event Categories Now Available:</strong></p>";
    echo "<ul style='margin: 10px 0; padding-left: 20px;'>";
    echo "<li>🥊 Boxing & Fighting Sports</li>";
    echo "<li>⚽ Football & Soccer</li>";
    echo "<li>🤾 Handball & Court Sports</li>";
    echo "<li>🏀 Basketball & Arena Sports</li>";
    echo "<li>🎾 Tennis & Racquet Sports</li>";
    echo "<li>🏐 Volleyball & Team Sports</li>";
    echo "<li>🏃 Marathon & Running Events</li>";
    echo "<li>🏊 Swimming & Aquatic Sports</li>";
    echo "<li>🎵 Music Concerts & Festivals</li>";
    echo "<li>😂 Comedy & Entertainment Shows</li>";
    echo "<li>👗 Fashion & Style Events</li>";
    echo "<li>🎮 Gaming & Esports</li>";
    echo "<li>💃 Dance & Cultural Performances</li>";
    echo "<li>🎭 Theater & Magic Shows</li>";
    echo "<li>🎬 Film Festivals & Cinema</li>";
    echo "<li>🍽️ Food & Culinary Events</li>";
    echo "<li>💼 Business & Professional Events</li>";
    echo "<li>🎨 Art & Cultural Exhibitions</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red; border-radius: 8px; background: #ffe6e6;'>";
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>🎉 Your Event Booking System is Now Complete!</h2>";
echo "<p style='font-size: 1.1em; color: #28a745; font-weight: bold;'>Congratulations! You now have a comprehensive event booking system with diverse, exciting events!</p>";
echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='events.php' style='background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; font-size: 1.1em; margin: 10px; display: inline-block;'>🎪 View All Events</a>";
echo "<a href='index.php' style='background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; font-size: 1.1em; margin: 10px; display: inline-block;'>🏠 Go to Homepage</a>";
echo "</div>";
?>
