/* Custom styles for Event Booking System */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    font-weight: 400;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    letter-spacing: -0.025em;
}

/* Modern Event Image Styles */
.event-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.event-image-container:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.18);
}

.event-image {
    width: 100%;
    height: 250px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 16px;
}

.event-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.event-image .event-icon {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

/* Event Type Specific Gradients */
.event-image.tech {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.event-image.music {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.event-image.business {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.event-image.art {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.event-image.food {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.event-image.default {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Event Badge Styles */
.event-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

/* Enhanced Event Category Text */
.event-category {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Improved Card Body */
.event-card .card-body {
    padding: 2rem 1.5rem;
    background: white;
}

/* Enhanced Progress Bar */
.progress-enhanced {
    height: 6px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.progress-enhanced .progress-bar {
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Enhanced Buttons */
.btn-enhanced {
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Enhanced Pagination */
.pagination-enhanced .page-link {
    border: none;
    border-radius: 12px;
    margin: 0 4px;
    padding: 12px 16px;
    font-weight: 600;
    color: var(--primary-color);
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.pagination-enhanced .page-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.pagination-enhanced .page-item.active .page-link {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 16px rgba(13, 110, 253, 0.3);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Enhanced Event Cards */
.event-card {
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    margin-bottom: 2rem;
    background: white;
    position: relative;
}

.event-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.event-card img {
    height: 250px;
    object-fit: cover;
    width: 100%;
    transition: transform 0.4s ease;
}

.event-card:hover img {
    transform: scale(1.05);
}

.event-card .event-image {
    height: 250px;
    border-radius: 20px 20px 0 0;
}

.event-card .card-body {
    padding: 1.5rem;
}

.event-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.event-card .event-date {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.event-card .event-location {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.event-card .event-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Search and Filter Section */
.search-section {
    background-color: var(--light-color);
    padding: 2rem 0;
    border-bottom: 1px solid #dee2e6;
}

.search-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Cart Styles */
.cart-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.cart-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid #dee2e6;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-controls input {
    width: 60px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 0.25rem;
}

/* Dashboard Styles */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.dashboard-card .card-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    padding: 1rem 1.5rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card p {
    font-size: 1.1rem;
    margin: 0;
}

/* Booking History */
.booking-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    position: relative;
}

.booking-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.booking-status .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

.qr-code {
    text-align: center;
    margin-top: 1rem;
}

.qr-code img {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.5rem;
    background: white;
}

/* Form Styles */
.form-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
}

.form-container h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, var(--primary-color));
    transform: translateY(-2px);
}

/* Event Details */
.event-details {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 2rem 0;
}

.event-details img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.event-details .content {
    padding: 2rem;
}

.event-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 1.5rem 0;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: 10px;
}

.event-meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.event-meta-item i {
    color: var(--primary-color);
    width: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .search-form {
        padding: 1rem;
    }
    
    .event-meta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .cart-item {
        padding: 0.75rem;
    }
    
    .form-container {
        padding: 1rem;
        margin: 1rem 0;
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Enhanced Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
    opacity: 0.5;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Modern Search Section */
.search-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-form {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Enhanced Form Controls */
.form-control {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    transform: translateY(-1px);
}

/* Enhanced Select Dropdown */
.form-select {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
}

/* Modern Alert Styles */
.alert {
    border: none;
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

/* Enhanced Card Hover Effects */
.card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card:hover {
    transform: translateY(-8px);
}

/* Modern Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* High-Quality Event Images */
.event-image-high-quality {
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.event-image-high-quality:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* Image Loading States */
.event-image-high-quality {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    background-size: 400% 400%;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.event-image-high-quality[src] {
    animation: none;
    background: none;
}

/* Image Overlay Effects */
.event-card .position-relative::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
    border-radius: 16px 16px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.event-card:hover .position-relative::after {
    opacity: 1;
}

/* Enhanced Image Quality */
.event-image-high-quality {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Lazy Loading Placeholder */
.event-image-high-quality[loading="lazy"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: shimmer 2s ease-in-out infinite;
}

/* Sports Event Specific Styles */
.sports-event-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.entertainment-event-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #a55eea, #8854d0);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

/* Event Category Icons */
.event-category-icon {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    z-index: 10;
}

/* Sports Event Hover Effects */
.sports-event:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
}

.entertainment-event:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(165, 94, 234, 0.2);
}

/* Event Type Specific Gradients for Borders */
.boxing-event {
    border-left: 4px solid #ff6b6b;
}

.football-event {
    border-left: 4px solid #2ed573;
}

.basketball-event {
    border-left: 4px solid #ffa502;
}

.handball-event {
    border-left: 4px solid #3742fa;
}

.comedy-event {
    border-left: 4px solid #ff9ff3;
}

.fashion-event {
    border-left: 4px solid #a55eea;
}

.gaming-event {
    border-left: 4px solid #70a1ff;
}

.dance-event {
    border-left: 4px solid #7bed9f;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    .event-image-high-quality {
        height: 200px !important;
        border-radius: 12px;
    }

    .search-form {
        padding: 1.5rem;
        border-radius: 16px;
    }

    .btn-enhanced {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .event-card .position-relative::after {
        border-radius: 12px 12px 0 0;
    }

    .sports-event-badge,
    .entertainment-event-badge {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .event-category-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}
