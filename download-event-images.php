<?php
/**
 * High-Quality Event Image Downloader
 * Downloads unique, high-quality images for each event type
 */

echo "<h1>🖼️ Downloading High-Quality Event Images</h1>";
echo "<p>This script will download unique, professional images for each event type.</p>";

// Create images directory if it doesn't exist
$imageDir = 'images/events/';
if (!is_dir($imageDir)) {
    mkdir($imageDir, 0777, true);
    echo "<p>✅ Created directory: $imageDir</p>";
}

// High-quality image sources for each event type
$eventImages = [
    'tech-conference' => [
        'url' => 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'tech-conference.jpg',
        'description' => 'Modern technology conference with laptops and presentations'
    ],
    'music-festival' => [
        'url' => 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'music-festival.jpg',
        'description' => 'Live music concert with stage lights and crowd'
    ],
    'business-workshop' => [
        'url' => 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'business-workshop.jpg',
        'description' => 'Professional business meeting and workshop setting'
    ],
    'art-exhibition' => [
        'url' => 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'art-exhibition.jpg',
        'description' => 'Modern art gallery with paintings and sculptures'
    ],
    'food-wine' => [
        'url' => 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'food-wine.jpg',
        'description' => 'Elegant dining setup with wine and gourmet food'
    ],
    'business-expo' => [
        'url' => 'https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'business-expo.jpg',
        'description' => 'Large business exhibition hall with booths'
    ],
    'adventure-outdoor' => [
        'url' => 'https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'adventure-outdoor.jpg',
        'description' => 'Mountain hiking and outdoor adventure activities'
    ],
    'default-event' => [
        'url' => 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'default-event.jpg',
        'description' => 'General event celebration with people and lights'
    ]
];

// Additional Cameroon-specific images
$cameroonImages = [
    'cameroon-culture' => [
        'url' => 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'cameroon-culture.jpg',
        'description' => 'African cultural event and traditional celebrations'
    ],
    'yaunde-venue' => [
        'url' => 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'yaunde-venue.jpg',
        'description' => 'Modern conference venue in Yaoundé'
    ],
    'douala-business' => [
        'url' => 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
        'filename' => 'douala-business.jpg',
        'description' => 'Business district and modern buildings'
    ]
];

// Merge all images
$allImages = array_merge($eventImages, $cameroonImages);

echo "<h2>📥 Starting Image Downloads...</h2>";

$successCount = 0;
$totalCount = count($allImages);

foreach ($allImages as $key => $imageData) {
    $url = $imageData['url'];
    $filename = $imageData['filename'];
    $filepath = $imageDir . $filename;
    $description = $imageData['description'];
    
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<strong>$key:</strong> $description<br>";
    echo "<small>URL: $url</small><br>";
    
    // Check if file already exists
    if (file_exists($filepath)) {
        echo "<span style='color: blue;'>⚠️ File already exists: $filename</span>";
    } else {
        // Download the image
        $imageData = @file_get_contents($url);
        
        if ($imageData !== false) {
            if (file_put_contents($filepath, $imageData)) {
                $fileSize = round(filesize($filepath) / 1024, 2);
                echo "<span style='color: green;'>✅ Downloaded: $filename ($fileSize KB)</span>";
                $successCount++;
            } else {
                echo "<span style='color: red;'>❌ Failed to save: $filename</span>";
            }
        } else {
            echo "<span style='color: red;'>❌ Failed to download: $filename</span>";
        }
    }
    echo "</div>";
    
    // Add a small delay to be respectful to the server
    usleep(500000); // 0.5 seconds
}

echo "<h2>📊 Download Summary</h2>";
echo "<p><strong>Total Images:</strong> $totalCount</p>";
echo "<p><strong>Successfully Downloaded:</strong> $successCount</p>";
echo "<p><strong>Already Existed:</strong> " . ($totalCount - $successCount) . "</p>";

// Create image mapping file
$imageMappingContent = "<?php
/**
 * Event Image Mapping
 * Maps event types to their corresponding image files
 */

\$eventImageMapping = [
    'tech' => 'events/tech-conference.jpg',
    'music' => 'events/music-festival.jpg',
    'business' => 'events/business-workshop.jpg',
    'art' => 'events/art-exhibition.jpg',
    'food' => 'events/food-wine.jpg',
    'expo' => 'events/business-expo.jpg',
    'adventure' => 'events/adventure-outdoor.jpg',
    'default' => 'events/default-event.jpg',
    'culture' => 'events/cameroon-culture.jpg',
    'yaunde' => 'events/yaunde-venue.jpg',
    'douala' => 'events/douala-business.jpg'
];

/**
 * Get image path for event type
 */
function getEventImage(\$eventName) {
    global \$eventImageMapping;
    
    \$eventName = strtolower(\$eventName);
    
    if (strpos(\$eventName, 'tech') !== false || strpos(\$eventName, 'conference') !== false) {
        return \$eventImageMapping['tech'];
    } elseif (strpos(\$eventName, 'music') !== false || strpos(\$eventName, 'festival') !== false || strpos(\$eventName, 'makossa') !== false) {
        return \$eventImageMapping['music'];
    } elseif (strpos(\$eventName, 'business') !== false || strpos(\$eventName, 'workshop') !== false || strpos(\$eventName, 'entrepreneur') !== false) {
        return \$eventImageMapping['business'];
    } elseif (strpos(\$eventName, 'art') !== false || strpos(\$eventName, 'exhibition') !== false) {
        return \$eventImageMapping['art'];
    } elseif (strpos(\$eventName, 'food') !== false || strpos(\$eventName, 'wine') !== false) {
        return \$eventImageMapping['food'];
    } elseif (strpos(\$eventName, 'expo') !== false || strpos(\$eventName, 'trade') !== false) {
        return \$eventImageMapping['expo'];
    } elseif (strpos(\$eventName, 'adventure') !== false || strpos(\$eventName, 'outdoor') !== false || strpos(\$eventName, 'mountain') !== false) {
        return \$eventImageMapping['adventure'];
    } elseif (strpos(\$eventName, 'yaunde') !== false || strpos(\$eventName, 'yaoundé') !== false) {
        return \$eventImageMapping['yaunde'];
    } elseif (strpos(\$eventName, 'douala') !== false) {
        return \$eventImageMapping['douala'];
    } else {
        return \$eventImageMapping['default'];
    }
}
?>";

file_put_contents('includes/image-mapping.php', $imageMappingContent);
echo "<p>✅ Created image mapping file: includes/image-mapping.php</p>";

echo "<h2>🎉 Image Download Complete!</h2>";
echo "<p>All high-quality images have been downloaded and are ready to use.</p>";
echo "<p><a href='events.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Events with New Images</a></p>";
?>
