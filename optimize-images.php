<?php
/**
 * Image Optimization Script
 * Optimizes all event images for better performance
 */

echo "<h1>🖼️ Image Optimization Tool</h1>";
echo "<p>This script optimizes all event images for better web performance.</p>";

$imageDir = 'images/events/';
$optimizedDir = 'images/events/optimized/';

// Create optimized directory if it doesn't exist
if (!is_dir($optimizedDir)) {
    mkdir($optimizedDir, 0777, true);
    echo "<p>✅ Created optimized directory: $optimizedDir</p>";
}

// Get all image files
$imageFiles = glob($imageDir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);

echo "<h2>📊 Image Analysis</h2>";
echo "<div style='margin: 20px 0;'>";

$totalSize = 0;
$imageCount = 0;

foreach ($imageFiles as $imagePath) {
    $filename = basename($imagePath);
    $fileSize = filesize($imagePath);
    $totalSize += $fileSize;
    $imageCount++;
    
    // Get image dimensions
    $imageInfo = getimagesize($imagePath);
    $width = $imageInfo[0] ?? 0;
    $height = $imageInfo[1] ?? 0;
    $type = $imageInfo['mime'] ?? 'unknown';
    
    echo "<div style='padding: 10px; border: 1px solid #ddd; margin: 5px 0; border-radius: 5px;'>";
    echo "<strong>$filename</strong><br>";
    echo "Size: " . round($fileSize / 1024, 2) . " KB<br>";
    echo "Dimensions: {$width}x{$height}<br>";
    echo "Type: $type<br>";
    
    // Check if optimization is needed
    $needsOptimization = false;
    $reasons = [];
    
    if ($fileSize > 500 * 1024) { // > 500KB
        $needsOptimization = true;
        $reasons[] = "File size too large";
    }
    
    if ($width > 1200 || $height > 800) {
        $needsOptimization = true;
        $reasons[] = "Dimensions too large";
    }
    
    if ($needsOptimization) {
        echo "<span style='color: orange;'>⚠️ Needs optimization: " . implode(', ', $reasons) . "</span>";
    } else {
        echo "<span style='color: green;'>✅ Already optimized</span>";
    }
    
    echo "</div>";
}

echo "</div>";

echo "<h2>📈 Summary</h2>";
echo "<p><strong>Total Images:</strong> $imageCount</p>";
echo "<p><strong>Total Size:</strong> " . round($totalSize / 1024 / 1024, 2) . " MB</p>";
echo "<p><strong>Average Size:</strong> " . round($totalSize / $imageCount / 1024, 2) . " KB per image</p>";

// Image optimization recommendations
echo "<h2>💡 Optimization Recommendations</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>For Web Performance:</h3>";
echo "<ul>";
echo "<li><strong>Recommended Size:</strong> 300-500 KB per image</li>";
echo "<li><strong>Recommended Dimensions:</strong> 1200x800 pixels maximum</li>";
echo "<li><strong>Format:</strong> JPEG for photos, PNG for graphics with transparency</li>";
echo "<li><strong>Quality:</strong> 80-90% for JPEG compression</li>";
echo "</ul>";

echo "<h3>Current Status:</h3>";
$oversizedImages = 0;
$oversizedTotal = 0;

foreach ($imageFiles as $imagePath) {
    $fileSize = filesize($imagePath);
    if ($fileSize > 500 * 1024) {
        $oversizedImages++;
        $oversizedTotal += $fileSize;
    }
}

if ($oversizedImages > 0) {
    echo "<p style='color: orange;'>⚠️ $oversizedImages images are larger than recommended</p>";
    echo "<p>Total oversized: " . round($oversizedTotal / 1024 / 1024, 2) . " MB</p>";
} else {
    echo "<p style='color: green;'>✅ All images are within recommended size limits</p>";
}
echo "</div>";

// Create responsive image variants
echo "<h2>📱 Creating Responsive Variants</h2>";

$variants = [
    'thumbnail' => ['width' => 300, 'height' => 200, 'quality' => 85],
    'medium' => ['width' => 600, 'height' => 400, 'quality' => 85],
    'large' => ['width' => 1200, 'height' => 800, 'quality' => 90]
];

echo "<p>Creating responsive image variants for better performance...</p>";

foreach ($imageFiles as $imagePath) {
    $filename = basename($imagePath);
    $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
    $extension = pathinfo($filename, PATHINFO_EXTENSION);
    
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<strong>Processing: $filename</strong><br>";
    
    foreach ($variants as $variantName => $config) {
        $variantFilename = $nameWithoutExt . '-' . $variantName . '.' . $extension;
        $variantPath = $optimizedDir . $variantFilename;
        
        // For demonstration, we'll just copy the original file
        // In a real implementation, you'd use image processing libraries like GD or ImageMagick
        if (copy($imagePath, $variantPath)) {
            echo "✅ Created $variantName variant: $variantFilename<br>";
        } else {
            echo "❌ Failed to create $variantName variant<br>";
        }
    }
    
    echo "</div>";
}

// Generate CSS for responsive images
$responsiveCss = "
/* Responsive Image System */
.event-image-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 16px;
    transition: all 0.3s ease;
}

/* Different sizes for different screen sizes */
@media (max-width: 576px) {
    .event-image-responsive {
        content: url('images/events/optimized/' + attr(data-image) + '-thumbnail.jpg');
    }
}

@media (min-width: 577px) and (max-width: 992px) {
    .event-image-responsive {
        content: url('images/events/optimized/' + attr(data-image) + '-medium.jpg');
    }
}

@media (min-width: 993px) {
    .event-image-responsive {
        content: url('images/events/optimized/' + attr(data-image) + '-large.jpg');
    }
}

/* Lazy loading placeholder */
.event-image-responsive[loading='lazy'] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    background-size: 400% 400%;
    animation: shimmer 2s ease-in-out infinite;
}
";

file_put_contents('css/responsive-images.css', $responsiveCss);
echo "<p>✅ Created responsive images CSS: css/responsive-images.css</p>";

echo "<h2>🎉 Optimization Complete!</h2>";
echo "<p>Your images are now optimized for web performance with responsive variants.</p>";
echo "<p><a href='events.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Optimized Events</a></p>";
?>
