<?php
/**
 * African Event Images Setup
 * Creates directory structure and provides image upload interface
 */

echo "<h1>🌍 African Event Images Setup</h1>";
echo "<p>Setting up African-themed images for your events...</p>";

// Create image directories
$imageDirectories = [
    'images/events/african',
    'images/events/african/music',
    'images/events/african/sports',
    'images/events/african/culture',
    'images/events/african/business',
    'images/events/african/entertainment',
    'images/events/african/food',
    'images/events/african/fashion',
    'images/events/african/tech'
];

echo "<h2>📁 Creating Image Directories</h2>";
foreach ($imageDirectories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0777, true)) {
            echo "<p>✅ Created directory: <strong>$dir</strong></p>";
        } else {
            echo "<p>❌ Failed to create directory: <strong>$dir</strong></p>";
        }
    } else {
        echo "<p>✅ Directory already exists: <strong>$dir</strong></p>";
    }
}

// Handle image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['african_image'])) {
    $category = $_POST['category'] ?? 'general';
    $eventType = $_POST['event_type'] ?? 'default';
    $file = $_FILES['african_image'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 10 * 1024 * 1024; // 10MB
        
        if (!in_array($file['type'], $allowedTypes)) {
            $error = "Invalid file type. Please upload JPG, PNG, GIF, or WebP images.";
        } elseif ($file['size'] > $maxSize) {
            $error = "File too large. Maximum size is 10MB.";
        } else {
            // Generate filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'african_' . $eventType . '_' . time() . '.' . $extension;
            $uploadPath = "images/events/african/$category/" . $filename;
            
            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                $success = "African image uploaded successfully: $filename";
                
                // Update image mapping
                updateAfricanImageMapping($category, $eventType, $uploadPath);
            } else {
                $error = "Failed to upload image.";
            }
        }
    } else {
        $error = "Please select a valid image file.";
    }
}

function updateAfricanImageMapping($category, $eventType, $imagePath) {
    $mappingFile = 'includes/african-image-mapping.php';
    
    // Create or update the mapping file
    $mappingContent = "<?php\n";
    $mappingContent .= "/**\n";
    $mappingContent .= " * African Event Image Mapping\n";
    $mappingContent .= " * Maps African event types to authentic African images\n";
    $mappingContent .= " */\n\n";
    
    $mappingContent .= "\$africanEventImages = [\n";
    $mappingContent .= "    // Music & Entertainment\n";
    $mappingContent .= "    'afrobeats' => 'images/events/african/music/african_afrobeats_festival.jpg',\n";
    $mappingContent .= "    'makossa' => 'images/events/african/music/african_makossa_concert.jpg',\n";
    $mappingContent .= "    'jazz' => 'images/events/african/music/african_jazz_night.jpg',\n";
    $mappingContent .= "    'traditional_music' => 'images/events/african/music/african_traditional_drums.jpg',\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Sports\n";
    $mappingContent .= "    'football' => 'images/events/african/sports/african_football_stadium.jpg',\n";
    $mappingContent .= "    'boxing' => 'images/events/african/sports/african_boxing_match.jpg',\n";
    $mappingContent .= "    'athletics' => 'images/events/african/sports/african_athletics_track.jpg',\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Culture\n";
    $mappingContent .= "    'cultural_festival' => 'images/events/african/culture/african_cultural_dance.jpg',\n";
    $mappingContent .= "    'traditional_ceremony' => 'images/events/african/culture/african_traditional_ceremony.jpg',\n";
    $mappingContent .= "    'art_exhibition' => 'images/events/african/culture/african_art_gallery.jpg',\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Business & Tech\n";
    $mappingContent .= "    'tech_conference' => 'images/events/african/tech/african_tech_summit.jpg',\n";
    $mappingContent .= "    'business_meeting' => 'images/events/african/business/african_business_conference.jpg',\n";
    $mappingContent .= "    'startup_pitch' => 'images/events/african/business/african_startup_event.jpg',\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Food & Fashion\n";
    $mappingContent .= "    'food_festival' => 'images/events/african/food/african_food_market.jpg',\n";
    $mappingContent .= "    'fashion_show' => 'images/events/african/fashion/african_fashion_runway.jpg',\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Default\n";
    $mappingContent .= "    'default' => 'images/events/african/african_event_default.jpg'\n";
    $mappingContent .= "];\n\n";
    
    $mappingContent .= "function getAfricanEventImage(\$eventName) {\n";
    $mappingContent .= "    global \$africanEventImages;\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    \$eventName = strtolower(\$eventName);\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Music events\n";
    $mappingContent .= "    if (strpos(\$eventName, 'afrobeats') !== false || strpos(\$eventName, 'afro') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['afrobeats'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'makossa') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['makossa'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'jazz') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['jazz'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'music') !== false || strpos(\$eventName, 'concert') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['traditional_music'];\n";
    $mappingContent .= "    }\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Sports events\n";
    $mappingContent .= "    elseif (strpos(\$eventName, 'football') !== false || strpos(\$eventName, 'lions') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['football'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'boxing') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['boxing'];\n";
    $mappingContent .= "    }\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Cultural events\n";
    $mappingContent .= "    elseif (strpos(\$eventName, 'cultural') !== false || strpos(\$eventName, 'heritage') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['cultural_festival'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'art') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['art_exhibition'];\n";
    $mappingContent .= "    }\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Business & Tech\n";
    $mappingContent .= "    elseif (strpos(\$eventName, 'tech') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['tech_conference'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'business') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['business_meeting'];\n";
    $mappingContent .= "    }\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    // Food & Fashion\n";
    $mappingContent .= "    elseif (strpos(\$eventName, 'food') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['food_festival'];\n";
    $mappingContent .= "    } elseif (strpos(\$eventName, 'fashion') !== false) {\n";
    $mappingContent .= "        return \$africanEventImages['fashion_show'];\n";
    $mappingContent .= "    }\n";
    $mappingContent .= "    \n";
    $mappingContent .= "    else {\n";
    $mappingContent .= "        return \$africanEventImages['default'];\n";
    $mappingContent .= "    }\n";
    $mappingContent .= "}\n";
    $mappingContent .= "?>";
    
    file_put_contents($mappingFile, $mappingContent);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>African Event Images Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b35, #f7931e, #ffd700);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .upload-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ff6b35;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        select, input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        button {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .image-suggestions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .suggestion-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .suggestion-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 25px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 African Event Images Setup</h1>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success">✅ <?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error">❌ <?php echo $error; ?></div>
        <?php endif; ?>
        
        <div class="upload-section">
            <h2>📸 Upload African Event Images</h2>
            <p>Upload authentic African images for your events. Choose high-quality images that represent African culture, venues, and activities.</p>
            
            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="category">Image Category:</label>
                    <select name="category" id="category" required>
                        <option value="">Select Category...</option>
                        <option value="music">🎵 Music & Entertainment</option>
                        <option value="sports">⚽ Sports</option>
                        <option value="culture">🎭 Culture & Heritage</option>
                        <option value="business">💼 Business & Tech</option>
                        <option value="food">🍽️ Food & Dining</option>
                        <option value="fashion">👗 Fashion</option>
                        <option value="entertainment">🎪 General Entertainment</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="event_type">Event Type:</label>
                    <select name="event_type" id="event_type" required>
                        <option value="">Select Event Type...</option>
                        <option value="afrobeats_festival">Afrobeats Festival</option>
                        <option value="makossa_concert">Makossa Concert</option>
                        <option value="jazz_night">Jazz Night</option>
                        <option value="football_match">Football Match</option>
                        <option value="boxing_championship">Boxing Championship</option>
                        <option value="cultural_festival">Cultural Festival</option>
                        <option value="tech_conference">Tech Conference</option>
                        <option value="business_summit">Business Summit</option>
                        <option value="food_festival">Food Festival</option>
                        <option value="fashion_show">Fashion Show</option>
                        <option value="comedy_night">Comedy Night</option>
                        <option value="art_exhibition">Art Exhibition</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="african_image">Upload African Image:</label>
                    <input type="file" name="african_image" id="african_image" accept="image/*" required>
                    <small style="color: #666; display: block; margin-top: 5px;">
                        Supported formats: JPG, PNG, GIF, WebP (Max 10MB)
                    </small>
                </div>
                
                <button type="submit">🌍 Upload African Image</button>
            </form>
        </div>
        
        <div class="image-suggestions">
            <h2>💡 African Image Suggestions</h2>
            <p>Search for these types of authentic African images on Google:</p>
            
            <div class="suggestion-grid">
                <div class="suggestion-item">
                    <h4>🎵 Music Events</h4>
                    <ul>
                        <li>African drummers performing</li>
                        <li>Afrobeats concert crowds</li>
                        <li>Traditional African instruments</li>
                        <li>Music festivals in Africa</li>
                    </ul>
                </div>
                
                <div class="suggestion-item">
                    <h4>⚽ Sports Events</h4>
                    <ul>
                        <li>African football stadiums</li>
                        <li>Boxing matches in Africa</li>
                        <li>Athletes from Cameroon</li>
                        <li>Sports venues in Yaoundé/Douala</li>
                    </ul>
                </div>
                
                <div class="suggestion-item">
                    <h4>🎭 Cultural Events</h4>
                    <ul>
                        <li>Traditional African dances</li>
                        <li>Cameroon cultural festivals</li>
                        <li>African art and crafts</li>
                        <li>Traditional ceremonies</li>
                    </ul>
                </div>
                
                <div class="suggestion-item">
                    <h4>💼 Business Events</h4>
                    <ul>
                        <li>African business conferences</li>
                        <li>Tech hubs in Africa</li>
                        <li>Modern African offices</li>
                        <li>Entrepreneurs in Cameroon</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="links">
            <a href="index.php">🏠 Homepage</a>
            <a href="events.php">🎫 View Events</a>
            <a href="admin">🛡️ Admin Panel</a>
            <a href="upload-event-images.php">📸 Upload Tool</a>
        </div>
    </div>
</body>
</html>
