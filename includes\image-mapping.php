<?php
/**
 * Event Image Mapping
 * Maps event types to their corresponding image files
 */

$eventImageMapping = [
    'tech' => 'events/tech-conference.jpg',
    'music' => 'events/music-festival.jpg',
    'business' => 'events/business-workshop.jpg',
    'art' => 'events/art-exhibition.jpg',
    'food' => 'events/food-wine.jpg',
    'expo' => 'events/business-expo.jpg',
    'adventure' => 'events/adventure-outdoor.jpg',
    'default' => 'events/default-event.jpg',
    'culture' => 'events/cameroon-culture.jpg',
    'yaunde' => 'events/yaunde-venue.jpg',
    'douala' => 'events/douala-business.jpg',
    // Sports & Entertainment
    'boxing' => 'events/sports/boxing-match.jpg',
    'football' => 'events/sports/football-stadium.jpg',
    'handball' => 'events/sports/handball-court.jpg',
    'basketball' => 'events/sports/basketball-arena.jpg',
    'comedy' => 'events/sports/comedy-show.jpg',
    'fashion' => 'events/sports/fashion-show.jpg',
    'gaming' => 'events/sports/gaming-tournament.jpg',
    'dance' => 'events/sports/dance-competition.jpg'
];

/**
 * Get image path for event type
 */
function getEventImage($eventName) {
    global $eventImageMapping;

    $eventName = strtolower($eventName);

    // Sports Events
    if (strpos($eventName, 'boxing') !== false || strpos($eventName, 'fight') !== false || strpos($eventName, 'championship') !== false) {
        return $eventImageMapping['boxing'];
    } elseif (strpos($eventName, 'football') !== false || strpos($eventName, 'soccer') !== false || strpos($eventName, 'derby') !== false || strpos($eventName, 'lions') !== false || strpos($eventName, 'eagles') !== false) {
        return $eventImageMapping['football'];
    } elseif (strpos($eventName, 'handball') !== false) {
        return $eventImageMapping['handball'];
    } elseif (strpos($eventName, 'basketball') !== false || strpos($eventName, 'all-stars') !== false) {
        return $eventImageMapping['basketball'];

    // Entertainment Events
    } elseif (strpos($eventName, 'comedy') !== false || strpos($eventName, 'comedian') !== false || strpos($eventName, 'funny') !== false) {
        return $eventImageMapping['comedy'];
    } elseif (strpos($eventName, 'fashion') !== false || strpos($eventName, 'runway') !== false || strpos($eventName, 'style') !== false) {
        return $eventImageMapping['fashion'];
    } elseif (strpos($eventName, 'gaming') !== false || strpos($eventName, 'esports') !== false || strpos($eventName, 'tournament') !== false) {
        return $eventImageMapping['gaming'];
    } elseif (strpos($eventName, 'dance') !== false || strpos($eventName, 'dancing') !== false || strpos($eventName, 'cultural') !== false) {
        return $eventImageMapping['dance'];

    // Original Categories
    } elseif (strpos($eventName, 'tech') !== false || strpos($eventName, 'conference') !== false) {
        return $eventImageMapping['tech'];
    } elseif (strpos($eventName, 'music') !== false || strpos($eventName, 'festival') !== false || strpos($eventName, 'makossa') !== false) {
        return $eventImageMapping['music'];
    } elseif (strpos($eventName, 'business') !== false || strpos($eventName, 'workshop') !== false || strpos($eventName, 'entrepreneur') !== false) {
        return $eventImageMapping['business'];
    } elseif (strpos($eventName, 'art') !== false || strpos($eventName, 'exhibition') !== false) {
        return $eventImageMapping['art'];
    } elseif (strpos($eventName, 'food') !== false || strpos($eventName, 'wine') !== false) {
        return $eventImageMapping['food'];
    } elseif (strpos($eventName, 'expo') !== false || strpos($eventName, 'trade') !== false) {
        return $eventImageMapping['expo'];
    } elseif (strpos($eventName, 'adventure') !== false || strpos($eventName, 'outdoor') !== false || strpos($eventName, 'mountain') !== false) {
        return $eventImageMapping['adventure'];
    } elseif (strpos($eventName, 'yaunde') !== false || strpos($eventName, 'yaoundé') !== false) {
        return $eventImageMapping['yaunde'];
    } elseif (strpos($eventName, 'douala') !== false) {
        return $eventImageMapping['douala'];
    } else {
        return $eventImageMapping['default'];
    }
}
?>