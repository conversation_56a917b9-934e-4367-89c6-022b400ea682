
/* Responsive Image System */
.event-image-responsive {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 16px;
    transition: all 0.3s ease;
}

/* Different sizes for different screen sizes */
@media (max-width: 576px) {
    .event-image-responsive {
        content: url('images/events/optimized/' + attr(data-image) + '-thumbnail.jpg');
    }
}

@media (min-width: 577px) and (max-width: 992px) {
    .event-image-responsive {
        content: url('images/events/optimized/' + attr(data-image) + '-medium.jpg');
    }
}

@media (min-width: 993px) {
    .event-image-responsive {
        content: url('images/events/optimized/' + attr(data-image) + '-large.jpg');
    }
}

/* Lazy loading placeholder */
.event-image-responsive[loading='lazy'] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    background-size: 400% 400%;
    animation: shimmer 2s ease-in-out infinite;
}
