# 📚 COMPLETE PROJECT DOCUMENTATION
## Event Booking System - Professional Documentation

### **🎯 Project Information**
- **Project Name:** Event Booking System
- **Version:** 1.0.0
- **Live URL:** https://eventbooking-z8ip.onrender.com
- **Admin Panel:** https://eventbooking-z8ip.onrender.com/admin
- **Repository:** https://github.com/EtahLarry/EventBooking
- **Documentation Date:** June 2025

---

## 📋 **TABLE OF CONTENTS**

1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Design & User Interface](#design--user-interface)
4. [Implementation Details](#implementation-details)
5. [Database Design](#database-design)
6. [Deployment Guide](#deployment-guide)
7. [Code Explanation](#code-explanation)
8. [User Manual](#user-manual)
9. [Admin Manual](#admin-manual)
10. [API Documentation](#api-documentation)
11. [Security Features](#security-features)
12. [Troubleshooting](#troubleshooting)
13. [Future Enhancements](#future-enhancements)

---

## 🎯 **PROJECT OVERVIEW**

### **Project Description**
The Event Booking System is a comprehensive web application designed for managing and booking events in Cameroon and Central Africa. It provides a professional platform for event organizers to showcase their events and for users to discover and book tickets seamlessly.

### **Key Features**
- **User Management:** Registration, login, profile management
- **Event Discovery:** Browse, search, and filter events
- **Booking System:** Add to cart, book tickets, manage bookings
- **Admin Panel:** Complete event and user management
- **Responsive Design:** Mobile-friendly interface
- **Cloud Deployment:** Production-ready on Render with PostgreSQL

### **Target Audience**
- **Primary:** Event attendees in Cameroon and Central Africa
- **Secondary:** Event organizers and administrators
- **Tertiary:** Developers and system administrators

### **Business Value**
- **Centralized Platform:** Single place for all events
- **Professional Presentation:** High-quality event showcasing
- **Efficient Booking:** Streamlined ticket purchasing process
- **Data Management:** Comprehensive analytics and reporting
- **Scalable Solution:** Ready for growth and expansion

---

## 🏗️ **SYSTEM ARCHITECTURE**

### **Technology Stack**

#### **Frontend Technologies:**
- **HTML5:** Semantic markup and structure
- **CSS3:** Modern styling with Flexbox and Grid
- **JavaScript:** Interactive functionality and AJAX
- **Bootstrap 5:** Responsive framework
- **Font Awesome:** Icon library

#### **Backend Technologies:**
- **PHP 8.2:** Server-side programming language
- **Apache 2.4:** Web server
- **PDO:** Database abstraction layer
- **Session Management:** User authentication

#### **Database:**
- **PostgreSQL 14:** Primary database (production)
- **MySQL 8.0:** Development database (Docker)

#### **Infrastructure:**
- **Docker:** Containerization
- **Render:** Cloud hosting platform
- **Git/GitHub:** Version control

### **Architecture Pattern**
The system follows a **Model-View-Controller (MVC)** pattern:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     VIEW        │    │   CONTROLLER    │    │     MODEL       │
│                 │    │                 │    │                 │
│ • HTML Pages    │◄──►│ • PHP Scripts   │◄──►│ • Database      │
│ • CSS Styles    │    │ • Functions     │    │ • Data Logic    │
│ • JavaScript    │    │ • Validation    │    │ • Business Rules│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **System Components**

#### **Core Modules:**
1. **Authentication Module:** User login/registration
2. **Event Management Module:** CRUD operations for events
3. **Booking Module:** Cart and booking functionality
4. **Admin Module:** Administrative functions
5. **Image Management Module:** Event image handling

#### **File Structure:**
```
event-booking-system/
├── index.php                 # Homepage
├── events.php                # Events listing
├── login.php                 # User login
├── register.php              # User registration
├── cart.php                  # Shopping cart
├── booking.php               # Booking process
├── admin/                    # Admin panel
│   ├── index.php            # Admin login
│   ├── dashboard.php        # Admin dashboard
│   └── includes/            # Admin functions
├── includes/                 # Core functions
│   ├── functions.php        # Main functions
│   ├── database.php         # Database config
│   └── image-mapping.php    # Image mapping
├── images/                   # Image assets
│   └── events/              # Event images
├── css/                      # Stylesheets
├── js/                       # JavaScript files
└── config/                   # Configuration files
```

---

## 🎨 **DESIGN & USER INTERFACE**

### **Design Philosophy**
The Event Booking System follows modern web design principles:

#### **Visual Design Principles:**
- **Clean & Minimalist:** Uncluttered interface focusing on content
- **Professional:** Business-appropriate color scheme and typography
- **Accessible:** High contrast and readable fonts
- **Consistent:** Uniform design language throughout

#### **Color Scheme:**
```css
Primary Colors:
- Primary Blue: #007bff
- Success Green: #28a745
- Warning Orange: #ffc107
- Danger Red: #dc3545

Secondary Colors:
- Light Gray: #f8f9fa
- Dark Gray: #343a40
- White: #ffffff
- Black: #000000
```

#### **Typography:**
- **Primary Font:** 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- **Headings:** Bold, hierarchical sizing
- **Body Text:** 16px base size for readability
- **Responsive:** Scales appropriately on all devices

### **User Experience (UX) Design**

#### **Navigation Structure:**
```
Homepage
├── Events (Browse/Search)
├── Login/Register
├── User Dashboard
│   ├── My Bookings
│   ├── Profile
│   └── Cart
└── Admin Panel
    ├── Dashboard
    ├── Events Management
    ├── Bookings Management
    └── Users Management
```

#### **User Journey:**
1. **Discovery:** User visits homepage, sees featured events
2. **Exploration:** Browse events page, use search/filters
3. **Selection:** View event details, add to cart
4. **Authentication:** Login or register if needed
5. **Booking:** Complete booking process
6. **Management:** View bookings in user dashboard

### **Responsive Design**

#### **Breakpoints:**
```css
/* Mobile First Approach */
Mobile: 320px - 767px
Tablet: 768px - 1023px
Desktop: 1024px+

/* Key Responsive Features */
- Flexible grid layouts
- Scalable images
- Touch-friendly buttons
- Collapsible navigation
- Optimized forms
```

#### **Mobile Optimizations:**
- **Touch Targets:** Minimum 44px for buttons
- **Readable Text:** Minimum 16px font size
- **Fast Loading:** Optimized images and code
- **Thumb Navigation:** Easy one-handed use

---

## 💻 **IMPLEMENTATION DETAILS**

### **Development Methodology**
The project was developed using **Agile methodology** with iterative improvements:

#### **Development Phases:**
1. **Planning & Design** (Week 1)
2. **Core Development** (Week 2-3)
3. **Testing & Debugging** (Week 4)
4. **Deployment & Optimization** (Week 5)
5. **Documentation & Maintenance** (Ongoing)

### **Code Standards**

#### **PHP Coding Standards:**
```php
<?php
/**
 * Function documentation
 * @param string $parameter Description
 * @return bool Success status
 */
function exampleFunction($parameter) {
    // Clear variable names
    $userInput = sanitizeInput($parameter);
    
    // Proper error handling
    try {
        $result = processData($userInput);
        return true;
    } catch (Exception $e) {
        error_log("Error: " . $e->getMessage());
        return false;
    }
}
?>
```

#### **HTML/CSS Standards:**
```html
<!-- Semantic HTML5 -->
<article class="event-card">
    <header class="event-header">
        <h2 class="event-title">Event Name</h2>
    </header>
    <main class="event-content">
        <p class="event-description">Description</p>
    </main>
</article>
```

```css
/* BEM Methodology */
.event-card {
    /* Block */
}

.event-card__title {
    /* Element */
}

.event-card--featured {
    /* Modifier */
}
```

### **Performance Optimizations**

#### **Frontend Optimizations:**
- **Image Optimization:** Lazy loading, WebP format
- **CSS Minification:** Compressed stylesheets
- **JavaScript Optimization:** Minified and deferred loading
- **Caching:** Browser caching headers

#### **Backend Optimizations:**
- **Database Indexing:** Optimized queries
- **Prepared Statements:** SQL injection prevention
- **Session Management:** Efficient user sessions
- **Error Handling:** Graceful error management

#### **Database Optimizations:**
```sql
-- Indexes for performance
CREATE INDEX idx_events_date ON events(date);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_bookings_user ON bookings(user_id);
CREATE INDEX idx_bookings_event ON bookings(event_id);
```

---

## 🗄️ **DATABASE DESIGN**

### **Database Schema**

#### **Entity Relationship Diagram:**
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    USERS    │    │   EVENTS    │    │  BOOKINGS   │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ id (PK)     │    │ id (PK)     │    │ id (PK)     │
│ username    │    │ name        │    │ user_id (FK)│
│ email       │    │ description │    │ event_id(FK)│
│ password    │    │ date        │    │ quantity    │
│ full_name   │    │ time        │    │ total_amount│
│ phone       │    │ venue       │    │ booking_ref │
│ created_at  │    │ location    │    │ status      │
└─────────────┘    │ organizer   │    │ created_at  │
                   │ price       │    └─────────────┘
                   │ total_tickets│
                   │ available_tickets│
                   │ image       │
                   │ status      │
                   │ created_at  │
                   └─────────────┘
```

#### **Table Definitions:**

##### **Users Table:**
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Events Table:**
```sql
CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    total_tickets INTEGER NOT NULL,
    available_tickets INTEGER NOT NULL,
    image VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Bookings Table:**
```sql
CREATE TABLE bookings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    event_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_reference VARCHAR(20) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'confirmed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Admin Users Table:**
```sql
CREATE TABLE admin_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Cart Items Table:**
```sql
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    event_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, event_id)
);
```

### **Data Relationships**
- **Users → Bookings:** One-to-Many (One user can have multiple bookings)
- **Events → Bookings:** One-to-Many (One event can have multiple bookings)
- **Users → Cart Items:** One-to-Many (One user can have multiple cart items)
- **Events → Cart Items:** One-to-Many (One event can be in multiple carts)

### **Data Integrity**
- **Primary Keys:** Unique identifiers for all tables
- **Foreign Keys:** Referential integrity between related tables
- **Constraints:** NOT NULL, UNIQUE, and CHECK constraints
- **Indexes:** Performance optimization for frequent queries

---

## 🚀 **DEPLOYMENT GUIDE**

### **Local Development Setup**

#### **Prerequisites:**
- Docker Desktop 4.0+
- Git 2.30+
- Web browser (Chrome, Firefox, Safari)
- Text editor (VS Code recommended)

#### **Quick Start:**
```bash
# 1. Clone the repository
git clone https://github.com/EtahLarry/EventBooking.git
cd EventBooking

# 2. Start with Docker
docker-compose up -d

# 3. Access the application
# Main App: http://localhost:8080
# Admin Panel: http://localhost:8080/admin
# phpMyAdmin: http://localhost:8082
```

#### **Environment Configuration:**
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### **Production Deployment (Render)**

#### **Step 1: Database Setup**
1. **Create PostgreSQL Database:**
   - Go to Render Dashboard
   - Click "New +" → "PostgreSQL"
   - Name: `eventbooking-database`
   - Copy External Database URL

#### **Step 2: Web Service Setup**
1. **Connect GitHub Repository:**
   - Click "New +" → "Web Service"
   - Connect GitHub account
   - Select repository: `EtahLarry/EventBooking`

2. **Configure Service:**
   - Name: `eventbooking-app`
   - Environment: Docker
   - Region: Choose closest to users
   - Instance Type: Free or Starter

#### **Step 3: Environment Variables**
Add these environment variables in Render:
```
DATABASE_URL=postgresql://username:password@host:port/database_name
```

#### **Step 4: Deploy**
1. Click "Create Web Service"
2. Wait for deployment (5-10 minutes)
3. Access your live application

### **Docker Configuration**

#### **Dockerfile:**
```dockerfile
FROM php:8.2-apache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev \
    libpq-dev zip unzip \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install \
    pdo_mysql pdo_pgsql mbstring exif pcntl bcmath gd

# Enable Apache rewrite module
RUN a2enmod rewrite

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . /var/www/html/

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Expose port 80
EXPOSE 80

# Start Apache
CMD ["apache2-foreground"]
```

#### **docker-compose.yml:**
```yaml
version: '3.8'

services:
  web:
    build: .
    container_name: event-booking-web
    ports:
      - "8080:80"
    volumes:
      - ./:/var/www/html
    depends_on:
      - mysql
    networks:
      - event-network

  mysql:
    image: mysql:8.0
    container_name: event-booking-db
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: eventuser
      MYSQL_PASSWORD: eventpass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
    ports:
      - "3307:3306"
    networks:
      - event-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: event-booking-phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: rootpassword
    ports:
      - "8082:80"
    depends_on:
      - mysql
    networks:
      - event-network

volumes:
  mysql_data:

networks:
  event-network:
    driver: bridge
```

### **SSL Configuration**

#### **Let's Encrypt Setup:**
```bash
# Install Certbot
sudo apt install certbot

# Generate SSL certificate
sudo certbot certonly --standalone -d yourdomain.com

# Auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### **Monitoring & Maintenance**

#### **Health Checks:**
```bash
#!/bin/bash
# health-check.sh

# Check application health
curl -f http://localhost:8080/health || exit 1

# Check database connection
docker exec event-booking-db mysql -u root -prootpassword -e "SELECT 1" || exit 1

echo "All services healthy"
```

#### **Backup Strategy:**
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Database backup
docker exec event-booking-db mysqldump -u root -prootpassword event_booking_system > $BACKUP_DIR/db_backup_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz images/ uploads/

echo "Backup completed: $DATE"
```

---

## 🔍 **CODE EXPLANATION**

### **Core Functions Overview**

#### **Database Connection (config/database.php):**
```php
<?php
/**
 * Database Configuration and Connection
 * Handles both PostgreSQL (production) and MySQL (development)
 */
function getDBConnection() {
    // Check if running on Render (production)
    if (isset($_ENV['DATABASE_URL'])) {
        // Parse PostgreSQL URL for Render
        $databaseUrl = parse_url($_ENV['DATABASE_URL']);
        $host = $databaseUrl['host'];
        $port = $databaseUrl['port'];
        $dbname = ltrim($databaseUrl['path'], '/');
        $username = $databaseUrl['user'];
        $password = $databaseUrl['pass'];

        $dsn = "pgsql:host=$host;port=$port;dbname=$dbname";
    } else {
        // Local MySQL configuration
        $dsn = "mysql:host=mysql;dbname=event_booking_system";
        $username = "eventuser";
        $password = "eventpass";
    }

    try {
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed");
    }
}
?>
```

#### **User Authentication (includes/functions.php):**
```php
<?php
/**
 * User Registration Function
 * Validates input, hashes password, creates user account
 */
function registerUser($username, $email, $password, $fullName, $phone = '') {
    $pdo = getDBConnection();

    // Validate input
    if (empty($username) || empty($email) || empty($password) || empty($fullName)) {
        return ['success' => false, 'message' => 'All required fields must be filled'];
    }

    // Check if user already exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);
    if ($stmt->fetch()) {
        return ['success' => false, 'message' => 'Username or email already exists'];
    }

    // Hash password securely
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

    try {
        // Insert new user
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, full_name, phone)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$username, $email, $hashedPassword, $fullName, $phone]);

        return ['success' => true, 'message' => 'Registration successful'];
    } catch (PDOException $e) {
        error_log("Registration error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Registration failed'];
    }
}

/**
 * User Login Function
 * Authenticates user credentials and creates session
 */
function loginUser($username, $password) {
    $pdo = getDBConnection();

    try {
        // Get user by username or email
        $stmt = $pdo->prepare("
            SELECT id, username, email, password, full_name
            FROM users
            WHERE username = ? OR email = ?
        ");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            // Create session
            session_start();
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['email'] = $user['email'];

            return ['success' => true, 'message' => 'Login successful'];
        } else {
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Login failed'];
    }
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    session_start();
    return isset($_SESSION['user_id']);
}
?>
```

#### **Event Management Functions:**
```php
<?php
/**
 * Get All Events with Search and Filter
 * Supports pagination, search, location, and date filtering
 */
function getAllEvents($search = '', $location = '', $date = '') {
    $pdo = getDBConnection();

    $sql = "SELECT * FROM events WHERE status = 'active'";
    $params = [];

    // Add search conditions
    if (!empty($search)) {
        $sql .= " AND (name ILIKE ? OR description ILIKE ? OR organizer ILIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    if (!empty($location)) {
        $sql .= " AND (location ILIKE ? OR venue ILIKE ?)";
        $locationTerm = "%$location%";
        $params[] = $locationTerm;
        $params[] = $locationTerm;
    }

    if (!empty($date)) {
        $sql .= " AND date = ?";
        $params[] = $date;
    }

    $sql .= " ORDER BY date ASC";

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get events error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get Single Event by ID
 */
function getEventById($eventId) {
    $pdo = getDBConnection();

    try {
        $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND status = 'active'");
        $stmt->execute([$eventId]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Get event error: " . $e->getMessage());
        return false;
    }
}

/**
 * Create New Event (Admin Function)
 */
function createEvent($eventData) {
    $pdo = getDBConnection();

    try {
        $stmt = $pdo->prepare("
            INSERT INTO events (name, description, date, time, venue, location,
                              organizer, organizer_contact, price, total_tickets,
                              available_tickets, image)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $eventData['name'],
            $eventData['description'],
            $eventData['date'],
            $eventData['time'],
            $eventData['venue'],
            $eventData['location'],
            $eventData['organizer'],
            $eventData['organizer_contact'],
            $eventData['price'],
            $eventData['total_tickets'],
            $eventData['total_tickets'], // available_tickets = total_tickets initially
            $eventData['image'] ?? null
        ]);

        return ['success' => true, 'message' => 'Event created successfully'];
    } catch (PDOException $e) {
        error_log("Create event error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to create event'];
    }
}
?>
```

#### **Shopping Cart Functions:**
```php
<?php
/**
 * Add Item to Cart
 * Handles cart item addition with quantity management
 */
function addToCart($userId, $eventId, $quantity = 1) {
    $pdo = getDBConnection();

    try {
        // Check if event exists and has available tickets
        $stmt = $pdo->prepare("SELECT available_tickets FROM events WHERE id = ?");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch();

        if (!$event || $event['available_tickets'] < $quantity) {
            return ['success' => false, 'message' => 'Not enough tickets available'];
        }

        // Check if item already in cart
        $stmt = $pdo->prepare("SELECT quantity FROM cart_items WHERE user_id = ? AND event_id = ?");
        $stmt->execute([$userId, $eventId]);
        $existingItem = $stmt->fetch();

        if ($existingItem) {
            // Update quantity
            $newQuantity = $existingItem['quantity'] + $quantity;
            $stmt = $pdo->prepare("UPDATE cart_items SET quantity = ? WHERE user_id = ? AND event_id = ?");
            $stmt->execute([$newQuantity, $userId, $eventId]);
        } else {
            // Add new item
            $stmt = $pdo->prepare("INSERT INTO cart_items (user_id, event_id, quantity) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $eventId, $quantity]);
        }

        return ['success' => true, 'message' => 'Item added to cart'];
    } catch (PDOException $e) {
        error_log("Add to cart error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to add to cart'];
    }
}

/**
 * Get User's Cart Items
 */
function getCartItems($userId) {
    $pdo = getDBConnection();

    try {
        $stmt = $pdo->prepare("
            SELECT ci.*, e.name, e.price, e.date, e.time, e.venue, e.image
            FROM cart_items ci
            JOIN events e ON ci.event_id = e.id
            WHERE ci.user_id = ? AND e.status = 'active'
            ORDER BY ci.added_at DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get cart items error: " . $e->getMessage());
        return [];
    }
}
?>
```

#### **Booking System Functions:**
```php
<?php
/**
 * Process Booking
 * Handles the complete booking process with transaction safety
 */
function processBooking($userId, $cartItems) {
    $pdo = getDBConnection();

    try {
        // Start transaction
        $pdo->beginTransaction();

        $totalAmount = 0;
        $bookingReference = generateBookingReference();

        foreach ($cartItems as $item) {
            // Verify ticket availability
            $stmt = $pdo->prepare("SELECT available_tickets, price FROM events WHERE id = ?");
            $stmt->execute([$item['event_id']]);
            $event = $stmt->fetch();

            if (!$event || $event['available_tickets'] < $item['quantity']) {
                throw new Exception("Not enough tickets available for event ID: " . $item['event_id']);
            }

            // Calculate amount
            $itemTotal = $event['price'] * $item['quantity'];
            $totalAmount += $itemTotal;

            // Create booking record
            $stmt = $pdo->prepare("
                INSERT INTO bookings (user_id, event_id, quantity, total_amount, booking_reference)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$userId, $item['event_id'], $item['quantity'], $itemTotal, $bookingReference]);

            // Update available tickets
            $stmt = $pdo->prepare("
                UPDATE events
                SET available_tickets = available_tickets - ?
                WHERE id = ?
            ");
            $stmt->execute([$item['quantity'], $item['event_id']]);
        }

        // Clear cart
        $stmt = $pdo->prepare("DELETE FROM cart_items WHERE user_id = ?");
        $stmt->execute([$userId]);

        // Commit transaction
        $pdo->commit();

        return [
            'success' => true,
            'message' => 'Booking successful',
            'booking_reference' => $bookingReference,
            'total_amount' => $totalAmount
        ];

    } catch (Exception $e) {
        // Rollback transaction
        $pdo->rollback();
        error_log("Booking error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Booking failed: ' . $e->getMessage()];
    }
}

/**
 * Generate Unique Booking Reference
 */
function generateBookingReference() {
    return 'BK' . date('Ymd') . strtoupper(substr(uniqid(), -6));
}

/**
 * Get User Bookings
 */
function getUserBookings($userId) {
    $pdo = getDBConnection();

    try {
        $stmt = $pdo->prepare("
            SELECT b.*, e.name as event_name, e.date, e.time, e.venue, e.location
            FROM bookings b
            JOIN events e ON b.event_id = e.id
            WHERE b.user_id = ?
            ORDER BY b.created_at DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Get user bookings error: " . $e->getMessage());
        return [];
    }
}
?>
```

### **Frontend JavaScript Functions**

#### **Cart Management (js/cart.js):**
```javascript
/**
 * Add to Cart Functionality
 * Handles AJAX requests for cart operations
 */
class CartManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Add to cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('add-to-cart')) {
                e.preventDefault();
                const eventId = e.target.dataset.eventId;
                const quantity = e.target.dataset.quantity || 1;
                this.addToCart(eventId, quantity);
            }
        });

        // Remove from cart buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-from-cart')) {
                e.preventDefault();
                const eventId = e.target.dataset.eventId;
                this.removeFromCart(eventId);
            }
        });
    }

    async addToCart(eventId, quantity = 1) {
        try {
            const response = await fetch('ajax/add-to-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event_id: eventId,
                    quantity: quantity
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Item added to cart!', 'success');
                this.updateCartCount();
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            this.showNotification('Failed to add item to cart', 'error');
        }
    }

    async removeFromCart(eventId) {
        try {
            const response = await fetch('ajax/remove-from-cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event_id: eventId
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('Item removed from cart', 'success');
                this.updateCartDisplay();
                this.updateCartCount();
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            console.error('Remove from cart error:', error);
            this.showNotification('Failed to remove item', 'error');
        }
    }

    async updateCartCount() {
        try {
            const response = await fetch('ajax/get-cart-count.php');
            const result = await response.json();

            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = result.count;
                element.style.display = result.count > 0 ? 'inline' : 'none';
            });
        } catch (error) {
            console.error('Update cart count error:', error);
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : 'success'} notification-toast`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    updateCartDisplay() {
        // Reload cart page if we're on it
        if (window.location.pathname.includes('cart.php')) {
            window.location.reload();
        }
    }
}

// Initialize cart manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CartManager();
});
```

#### **Form Validation (js/validation.js):**
```javascript
/**
 * Form Validation Class
 * Handles client-side form validation
 */
class FormValidator {
    constructor() {
        this.initializeValidation();
    }

    initializeValidation() {
        // Registration form validation
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => {
                if (!this.validateRegistrationForm(registerForm)) {
                    e.preventDefault();
                }
            });
        }

        // Login form validation
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                if (!this.validateLoginForm(loginForm)) {
                    e.preventDefault();
                }
            });
        }

        // Real-time validation
        this.addRealTimeValidation();
    }

    validateRegistrationForm(form) {
        const formData = new FormData(form);
        let isValid = true;

        // Username validation
        const username = formData.get('username');
        if (!username || username.length < 3) {
            this.showFieldError('username', 'Username must be at least 3 characters');
            isValid = false;
        } else {
            this.clearFieldError('username');
        }

        // Email validation
        const email = formData.get('email');
        if (!email || !this.isValidEmail(email)) {
            this.showFieldError('email', 'Please enter a valid email address');
            isValid = false;
        } else {
            this.clearFieldError('email');
        }

        // Password validation
        const password = formData.get('password');
        if (!password || password.length < 6) {
            this.showFieldError('password', 'Password must be at least 6 characters');
            isValid = false;
        } else {
            this.clearFieldError('password');
        }

        // Confirm password validation
        const confirmPassword = formData.get('confirm_password');
        if (password !== confirmPassword) {
            this.showFieldError('confirm_password', 'Passwords do not match');
            isValid = false;
        } else {
            this.clearFieldError('confirm_password');
        }

        // Full name validation
        const fullName = formData.get('full_name');
        if (!fullName || fullName.trim().length < 2) {
            this.showFieldError('full_name', 'Please enter your full name');
            isValid = false;
        } else {
            this.clearFieldError('full_name');
        }

        return isValid;
    }

    validateLoginForm(form) {
        const formData = new FormData(form);
        let isValid = true;

        // Username/Email validation
        const username = formData.get('username');
        if (!username) {
            this.showFieldError('username', 'Please enter your username or email');
            isValid = false;
        } else {
            this.clearFieldError('username');
        }

        // Password validation
        const password = formData.get('password');
        if (!password) {
            this.showFieldError('password', 'Please enter your password');
            isValid = false;
        } else {
            this.clearFieldError('password');
        }

        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    showFieldError(fieldName, message) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.add('is-invalid');

            // Remove existing error message
            const existingError = field.parentElement.querySelector('.invalid-feedback');
            if (existingError) {
                existingError.remove();
            }

            // Add new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = message;
            field.parentElement.appendChild(errorDiv);
        }
    }

    clearFieldError(fieldName) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.remove('is-invalid');
            const errorDiv = field.parentElement.querySelector('.invalid-feedback');
            if (errorDiv) {
                errorDiv.remove();
            }
        }
    }

    addRealTimeValidation() {
        // Add real-time validation for email fields
        const emailFields = document.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            field.addEventListener('blur', () => {
                if (field.value && !this.isValidEmail(field.value)) {
                    this.showFieldError(field.name, 'Please enter a valid email address');
                } else {
                    this.clearFieldError(field.name);
                }
            });
        });

        // Add real-time validation for password confirmation
        const confirmPasswordField = document.querySelector('[name="confirm_password"]');
        const passwordField = document.querySelector('[name="password"]');

        if (confirmPasswordField && passwordField) {
            confirmPasswordField.addEventListener('input', () => {
                if (confirmPasswordField.value !== passwordField.value) {
                    this.showFieldError('confirm_password', 'Passwords do not match');
                } else {
                    this.clearFieldError('confirm_password');
                }
            });
        }
    }
}

// Initialize form validator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FormValidator();
});
```

### **Security Implementation**

#### **Input Sanitization:**
```php
<?php
/**
 * Input Sanitization Functions
 * Prevents XSS and injection attacks
 */
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }

    // Remove whitespace and special characters
    $input = trim($input);
    $input = stripslashes($input);
    $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');

    return $input;
}

function sanitizeEmail($email) {
    return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function sanitizeUrl($url) {
    return filter_var(trim($url), FILTER_SANITIZE_URL);
}
?>
```

#### **CSRF Protection:**
```php
<?php
/**
 * CSRF Token Management
 * Prevents Cross-Site Request Forgery attacks
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function getCSRFField() {
    $token = generateCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}
?>
```

#### **SQL Injection Prevention:**
```php
<?php
/**
 * Safe Database Query Function
 * Uses prepared statements to prevent SQL injection
 */
function safeQuery($sql, $params = []) {
    $pdo = getDBConnection();

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Database query error: " . $e->getMessage());
        throw new Exception("Database operation failed");
    }
}

// Example usage:
// $users = safeQuery("SELECT * FROM users WHERE email = ?", [$email])->fetchAll();
?>
```
