# 📚 COMPLETE PROJECT DOCUMENTATION
## Event Booking System - Professional Documentation

### **🎯 Project Information**
- **Project Name:** Event Booking System
- **Version:** 1.0.0
- **Live URL:** https://eventbooking-z8ip.onrender.com
- **Admin Panel:** https://eventbooking-z8ip.onrender.com/admin
- **Repository:** https://github.com/EtahLarry/EventBooking
- **Documentation Date:** June 2025

---

## 📋 **TABLE OF CONTENTS**

1. [Project Overview](#project-overview)
2. [System Architecture](#system-architecture)
3. [Design & User Interface](#design--user-interface)
4. [Implementation Details](#implementation-details)
5. [Database Design](#database-design)
6. [Deployment Guide](#deployment-guide)
7. [Code Explanation](#code-explanation)
8. [User Manual](#user-manual)
9. [Admin Manual](#admin-manual)
10. [API Documentation](#api-documentation)
11. [Security Features](#security-features)
12. [Troubleshooting](#troubleshooting)
13. [Future Enhancements](#future-enhancements)

---

## 🎯 **PROJECT OVERVIEW**

### **Project Description**
The Event Booking System is a comprehensive web application designed for managing and booking events in Cameroon and Central Africa. It provides a professional platform for event organizers to showcase their events and for users to discover and book tickets seamlessly.

### **Key Features**
- **User Management:** Registration, login, profile management
- **Event Discovery:** Browse, search, and filter events
- **Booking System:** Add to cart, book tickets, manage bookings
- **Admin Panel:** Complete event and user management
- **Responsive Design:** Mobile-friendly interface
- **Cloud Deployment:** Production-ready on Render with PostgreSQL

### **Target Audience**
- **Primary:** Event attendees in Cameroon and Central Africa
- **Secondary:** Event organizers and administrators
- **Tertiary:** Developers and system administrators

### **Business Value**
- **Centralized Platform:** Single place for all events
- **Professional Presentation:** High-quality event showcasing
- **Efficient Booking:** Streamlined ticket purchasing process
- **Data Management:** Comprehensive analytics and reporting
- **Scalable Solution:** Ready for growth and expansion

---

## 🏗️ **SYSTEM ARCHITECTURE**

### **Technology Stack**

#### **Frontend Technologies:**
- **HTML5:** Semantic markup and structure
- **CSS3:** Modern styling with Flexbox and Grid
- **JavaScript:** Interactive functionality and AJAX
- **Bootstrap 5:** Responsive framework
- **Font Awesome:** Icon library

#### **Backend Technologies:**
- **PHP 8.2:** Server-side programming language
- **Apache 2.4:** Web server
- **PDO:** Database abstraction layer
- **Session Management:** User authentication

#### **Database:**
- **PostgreSQL 14:** Primary database (production)
- **MySQL 8.0:** Development database (Docker)

#### **Infrastructure:**
- **Docker:** Containerization
- **Render:** Cloud hosting platform
- **Git/GitHub:** Version control

### **Architecture Pattern**
The system follows a **Model-View-Controller (MVC)** pattern:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     VIEW        │    │   CONTROLLER    │    │     MODEL       │
│                 │    │                 │    │                 │
│ • HTML Pages    │◄──►│ • PHP Scripts   │◄──►│ • Database      │
│ • CSS Styles    │    │ • Functions     │    │ • Data Logic    │
│ • JavaScript    │    │ • Validation    │    │ • Business Rules│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **System Components**

#### **Core Modules:**
1. **Authentication Module:** User login/registration
2. **Event Management Module:** CRUD operations for events
3. **Booking Module:** Cart and booking functionality
4. **Admin Module:** Administrative functions
5. **Image Management Module:** Event image handling

#### **File Structure:**
```
event-booking-system/
├── index.php                 # Homepage
├── events.php                # Events listing
├── login.php                 # User login
├── register.php              # User registration
├── cart.php                  # Shopping cart
├── booking.php               # Booking process
├── admin/                    # Admin panel
│   ├── index.php            # Admin login
│   ├── dashboard.php        # Admin dashboard
│   └── includes/            # Admin functions
├── includes/                 # Core functions
│   ├── functions.php        # Main functions
│   ├── database.php         # Database config
│   └── image-mapping.php    # Image mapping
├── images/                   # Image assets
│   └── events/              # Event images
├── css/                      # Stylesheets
├── js/                       # JavaScript files
└── config/                   # Configuration files
```

---

## 🎨 **DESIGN & USER INTERFACE**

### **Design Philosophy**
The Event Booking System follows modern web design principles:

#### **Visual Design Principles:**
- **Clean & Minimalist:** Uncluttered interface focusing on content
- **Professional:** Business-appropriate color scheme and typography
- **Accessible:** High contrast and readable fonts
- **Consistent:** Uniform design language throughout

#### **Color Scheme:**
```css
Primary Colors:
- Primary Blue: #007bff
- Success Green: #28a745
- Warning Orange: #ffc107
- Danger Red: #dc3545

Secondary Colors:
- Light Gray: #f8f9fa
- Dark Gray: #343a40
- White: #ffffff
- Black: #000000
```

#### **Typography:**
- **Primary Font:** 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- **Headings:** Bold, hierarchical sizing
- **Body Text:** 16px base size for readability
- **Responsive:** Scales appropriately on all devices

### **User Experience (UX) Design**

#### **Navigation Structure:**
```
Homepage
├── Events (Browse/Search)
├── Login/Register
├── User Dashboard
│   ├── My Bookings
│   ├── Profile
│   └── Cart
└── Admin Panel
    ├── Dashboard
    ├── Events Management
    ├── Bookings Management
    └── Users Management
```

#### **User Journey:**
1. **Discovery:** User visits homepage, sees featured events
2. **Exploration:** Browse events page, use search/filters
3. **Selection:** View event details, add to cart
4. **Authentication:** Login or register if needed
5. **Booking:** Complete booking process
6. **Management:** View bookings in user dashboard

### **Responsive Design**

#### **Breakpoints:**
```css
/* Mobile First Approach */
Mobile: 320px - 767px
Tablet: 768px - 1023px
Desktop: 1024px+

/* Key Responsive Features */
- Flexible grid layouts
- Scalable images
- Touch-friendly buttons
- Collapsible navigation
- Optimized forms
```

#### **Mobile Optimizations:**
- **Touch Targets:** Minimum 44px for buttons
- **Readable Text:** Minimum 16px font size
- **Fast Loading:** Optimized images and code
- **Thumb Navigation:** Easy one-handed use

---

## 💻 **IMPLEMENTATION DETAILS**

### **Development Methodology**
The project was developed using **Agile methodology** with iterative improvements:

#### **Development Phases:**
1. **Planning & Design** (Week 1)
2. **Core Development** (Week 2-3)
3. **Testing & Debugging** (Week 4)
4. **Deployment & Optimization** (Week 5)
5. **Documentation & Maintenance** (Ongoing)

### **Code Standards**

#### **PHP Coding Standards:**
```php
<?php
/**
 * Function documentation
 * @param string $parameter Description
 * @return bool Success status
 */
function exampleFunction($parameter) {
    // Clear variable names
    $userInput = sanitizeInput($parameter);
    
    // Proper error handling
    try {
        $result = processData($userInput);
        return true;
    } catch (Exception $e) {
        error_log("Error: " . $e->getMessage());
        return false;
    }
}
?>
```

#### **HTML/CSS Standards:**
```html
<!-- Semantic HTML5 -->
<article class="event-card">
    <header class="event-header">
        <h2 class="event-title">Event Name</h2>
    </header>
    <main class="event-content">
        <p class="event-description">Description</p>
    </main>
</article>
```

```css
/* BEM Methodology */
.event-card {
    /* Block */
}

.event-card__title {
    /* Element */
}

.event-card--featured {
    /* Modifier */
}
```

### **Performance Optimizations**

#### **Frontend Optimizations:**
- **Image Optimization:** Lazy loading, WebP format
- **CSS Minification:** Compressed stylesheets
- **JavaScript Optimization:** Minified and deferred loading
- **Caching:** Browser caching headers

#### **Backend Optimizations:**
- **Database Indexing:** Optimized queries
- **Prepared Statements:** SQL injection prevention
- **Session Management:** Efficient user sessions
- **Error Handling:** Graceful error management

#### **Database Optimizations:**
```sql
-- Indexes for performance
CREATE INDEX idx_events_date ON events(date);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_bookings_user ON bookings(user_id);
CREATE INDEX idx_bookings_event ON bookings(event_id);
```

---

## 🗄️ **DATABASE DESIGN**

### **Database Schema**

#### **Entity Relationship Diagram:**
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    USERS    │    │   EVENTS    │    │  BOOKINGS   │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ id (PK)     │    │ id (PK)     │    │ id (PK)     │
│ username    │    │ name        │    │ user_id (FK)│
│ email       │    │ description │    │ event_id(FK)│
│ password    │    │ date        │    │ quantity    │
│ full_name   │    │ time        │    │ total_amount│
│ phone       │    │ venue       │    │ booking_ref │
│ created_at  │    │ location    │    │ status      │
└─────────────┘    │ organizer   │    │ created_at  │
                   │ price       │    └─────────────┘
                   │ total_tickets│
                   │ available_tickets│
                   │ image       │
                   │ status      │
                   │ created_at  │
                   └─────────────┘
```

#### **Table Definitions:**

##### **Users Table:**
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Events Table:**
```sql
CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    date DATE NOT NULL,
    time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    total_tickets INTEGER NOT NULL,
    available_tickets INTEGER NOT NULL,
    image VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Bookings Table:**
```sql
CREATE TABLE bookings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    event_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_reference VARCHAR(20) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'confirmed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Admin Users Table:**
```sql
CREATE TABLE admin_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **Cart Items Table:**
```sql
CREATE TABLE cart_items (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    event_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, event_id)
);
```

### **Data Relationships**
- **Users → Bookings:** One-to-Many (One user can have multiple bookings)
- **Events → Bookings:** One-to-Many (One event can have multiple bookings)
- **Users → Cart Items:** One-to-Many (One user can have multiple cart items)
- **Events → Cart Items:** One-to-Many (One event can be in multiple carts)

### **Data Integrity**
- **Primary Keys:** Unique identifiers for all tables
- **Foreign Keys:** Referential integrity between related tables
- **Constraints:** NOT NULL, UNIQUE, and CHECK constraints
- **Indexes:** Performance optimization for frequent queries

---

## 🚀 **DEPLOYMENT GUIDE**

### **Local Development Setup**

#### **Prerequisites:**
- Docker Desktop 4.0+
- Git 2.30+
- Web browser (Chrome, Firefox, Safari)
- Text editor (VS Code recommended)

#### **Quick Start:**
```bash
# 1. Clone the repository
git clone https://github.com/EtahLarry/EventBooking.git
cd EventBooking

# 2. Start with Docker
docker-compose up -d

# 3. Access the application
# Main App: http://localhost:8080
# Admin Panel: http://localhost:8080/admin
# phpMyAdmin: http://localhost:8082
```

#### **Environment Configuration:**
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### **Production Deployment (Render)**

#### **Step 1: Database Setup**
1. **Create PostgreSQL Database:**
   - Go to Render Dashboard
   - Click "New +" → "PostgreSQL"
   - Name: `eventbooking-database`
   - Copy External Database URL

#### **Step 2: Web Service Setup**
1. **Connect GitHub Repository:**
   - Click "New +" → "Web Service"
   - Connect GitHub account
   - Select repository: `EtahLarry/EventBooking`

2. **Configure Service:**
   - Name: `eventbooking-app`
   - Environment: Docker
   - Region: Choose closest to users
   - Instance Type: Free or Starter

#### **Step 3: Environment Variables**
Add these environment variables in Render:
```
DATABASE_URL=postgresql://username:password@host:port/database_name
```

#### **Step 4: Deploy**
1. Click "Create Web Service"
2. Wait for deployment (5-10 minutes)
3. Access your live application

### **Docker Configuration**

#### **Dockerfile:**
```dockerfile
FROM php:8.2-apache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev \
    libpq-dev zip unzip \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install \
    pdo_mysql pdo_pgsql mbstring exif pcntl bcmath gd

# Enable Apache rewrite module
RUN a2enmod rewrite

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . /var/www/html/

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Expose port 80
EXPOSE 80

# Start Apache
CMD ["apache2-foreground"]
```

#### **docker-compose.yml:**
```yaml
version: '3.8'

services:
  web:
    build: .
    container_name: event-booking-web
    ports:
      - "8080:80"
    volumes:
      - ./:/var/www/html
    depends_on:
      - mysql
    networks:
      - event-network

  mysql:
    image: mysql:8.0
    container_name: event-booking-db
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: eventuser
      MYSQL_PASSWORD: eventpass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
    ports:
      - "3307:3306"
    networks:
      - event-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: event-booking-phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: rootpassword
    ports:
      - "8082:80"
    depends_on:
      - mysql
    networks:
      - event-network

volumes:
  mysql_data:

networks:
  event-network:
    driver: bridge
```

### **SSL Configuration**

#### **Let's Encrypt Setup:**
```bash
# Install Certbot
sudo apt install certbot

# Generate SSL certificate
sudo certbot certonly --standalone -d yourdomain.com

# Auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### **Monitoring & Maintenance**

#### **Health Checks:**
```bash
#!/bin/bash
# health-check.sh

# Check application health
curl -f http://localhost:8080/health || exit 1

# Check database connection
docker exec event-booking-db mysql -u root -prootpassword -e "SELECT 1" || exit 1

echo "All services healthy"
```

#### **Backup Strategy:**
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Database backup
docker exec event-booking-db mysqldump -u root -prootpassword event_booking_system > $BACKUP_DIR/db_backup_$DATE.sql

# Files backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz images/ uploads/

echo "Backup completed: $DATE"
```
