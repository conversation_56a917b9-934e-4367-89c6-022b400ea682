<?php
/**
 * Database Connection Information
 * Shows database details and connection methods
 */

session_start();
require_once 'config/database.php';

// Simple authentication
$admin_password = 'admin123';

if (!isset($_SESSION['db_info_logged_in'])) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['admin_password'])) {
        if ($_POST['admin_password'] === $admin_password) {
            $_SESSION['db_info_logged_in'] = true;
        } else {
            $error = "Invalid password";
        }
    }
    
    if (!isset($_SESSION['db_info_logged_in'])) {
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Database Info Access</title>
            <style>
                body { font-family: Arial, sans-serif; background: #f4f4f4; padding: 50px; }
                .login-form { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
                input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
                button { background: #FF6B35; color: white; padding: 12px 20px; border: none; border-radius: 5px; cursor: pointer; }
                .error { color: red; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="login-form">
                <h2>🔐 Database Info Access</h2>
                <?php if (isset($error)) echo "<div class='error'>$error</div>"; ?>
                <form method="POST">
                    <input type="password" name="admin_password" placeholder="Admin Password" required>
                    <button type="submit">View Database Info</button>
                </form>
            </div>
        </body>
        </html>
        <?php
        exit;
    }
}

// Get database connection info
$db_info = [];
$connection_status = false;

try {
    $pdo = getDBConnection();
    $connection_status = true;
    
    // Get database version
    $stmt = $pdo->query("SELECT version()");
    $db_info['version'] = $stmt->fetchColumn();
    
    // Get current database name
    $stmt = $pdo->query("SELECT current_database()");
    $db_info['database_name'] = $stmt->fetchColumn();
    
    // Get current user
    $stmt = $pdo->query("SELECT current_user");
    $db_info['current_user'] = $stmt->fetchColumn();
    
    // Get table count
    $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'");
    $db_info['table_count'] = $stmt->fetchColumn();
    
    // Get database size
    $stmt = $pdo->query("SELECT pg_size_pretty(pg_database_size(current_database()))");
    $db_info['database_size'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $connection_error = $e->getMessage();
}

// Parse DATABASE_URL if available
$database_url = $_ENV['DATABASE_URL'] ?? getenv('DATABASE_URL') ?? null;
$parsed_url = null;

if ($database_url) {
    $parsed_url = parse_url($database_url);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Information</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #2C3E50;
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .status-card {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border: 1px solid #c3e6cb;
            text-align: center;
        }

        .status-card.error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #FF6B35;
        }

        .info-card h3 {
            color: #2C3E50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-value {
            color: #FF6B35;
            font-family: 'Courier New', monospace;
            background: rgba(255, 107, 53, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            word-break: break-all;
        }

        .connection-methods {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .connection-methods h3 {
            color: #1976d2;
            margin-bottom: 20px;
        }

        .method-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #1976d2;
        }

        .method-card h4 {
            color: #2C3E50;
            margin-bottom: 10px;
        }

        .method-card p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .code-block {
            background: #2C3E50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 20px 0;
        }

        .links {
            text-align: center;
            margin-top: 30px;
        }

        .links a {
            display: inline-block;
            margin: 10px;
            padding: 12px 25px;
            background: #FF6B35;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: background 0.3s;
        }

        .links a:hover {
            background: #e55a2b;
        }

        .links a.secondary {
            background: #6c757d;
        }

        .links a.secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Database Connection Information</h1>
            <p>Event Booking System - PostgreSQL Database</p>
        </div>

        <div class="content">
            <?php if ($connection_status): ?>
                <div class="status-card">
                    <h2>✅ Database Connection Successful!</h2>
                    <p>Your PostgreSQL database is connected and accessible.</p>
                </div>

                <div class="info-grid">
                    <div class="info-card">
                        <h3>📊 Database Information</h3>
                        <div class="info-item">
                            <span class="info-label">Database Name:</span>
                            <span class="info-value"><?php echo htmlspecialchars($db_info['database_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Current User:</span>
                            <span class="info-value"><?php echo htmlspecialchars($db_info['current_user']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Table Count:</span>
                            <span class="info-value"><?php echo htmlspecialchars($db_info['table_count']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Database Size:</span>
                            <span class="info-value"><?php echo htmlspecialchars($db_info['database_size']); ?></span>
                        </div>
                    </div>

                    <div class="info-card">
                        <h3>🔧 Server Information</h3>
                        <div class="info-item">
                            <span class="info-label">PostgreSQL Version:</span>
                            <span class="info-value"><?php echo htmlspecialchars(explode(' ', $db_info['version'])[1]); ?></span>
                        </div>
                        <?php if ($parsed_url): ?>
                            <div class="info-item">
                                <span class="info-label">Host:</span>
                                <span class="info-value"><?php echo htmlspecialchars($parsed_url['host']); ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Port:</span>
                                <span class="info-value"><?php echo htmlspecialchars($parsed_url['port'] ?? '5432'); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            <?php else: ?>
                <div class="status-card error">
                    <h2>❌ Database Connection Failed!</h2>
                    <p>Error: <?php echo htmlspecialchars($connection_error ?? 'Unknown error'); ?></p>
                </div>
            <?php endif; ?>

            <div class="connection-methods">
                <h3>🔗 Database Access Methods</h3>

                <div class="method-card">
                    <h4>1. Web-Based Admin Panel (Recommended)</h4>
                    <p>Use the built-in database admin panel for easy web-based access:</p>
                    <div class="code-block">
                        URL: https://eventbooking-z8ip.onrender.com/database-admin.php
                        Password: admin123
                    </div>
                </div>

                <div class="method-card">
                    <h4>2. pgAdmin (Desktop Application)</h4>
                    <p>Download and install pgAdmin for a full-featured database management interface:</p>
                    <div class="code-block">
                        Download: https://www.pgadmin.org/download/
                        <?php if ($parsed_url): ?>
Host: <?php echo $parsed_url['host']; ?>
Port: <?php echo $parsed_url['port'] ?? '5432'; ?>
Database: <?php echo ltrim($parsed_url['path'], '/'); ?>
Username: <?php echo $parsed_url['user']; ?>
Password: [Your database password]
                        <?php endif; ?>
                    </div>
                </div>

                <div class="method-card">
                    <h4>3. Command Line (psql)</h4>
                    <p>Connect using PostgreSQL command line tool:</p>
                    <div class="code-block">
                        <?php if ($database_url): ?>
psql "<?php echo $database_url; ?>"
                        <?php else: ?>
psql -h [host] -p [port] -U [username] -d [database]
                        <?php endif; ?>
                    </div>
                </div>

                <div class="method-card">
                    <h4>4. DBeaver (Free Universal Database Tool)</h4>
                    <p>Download DBeaver for a free, cross-platform database management tool:</p>
                    <div class="code-block">
                        Download: https://dbeaver.io/download/
                        Connection Type: PostgreSQL
                        <?php if ($parsed_url): ?>
Host: <?php echo $parsed_url['host']; ?>
Port: <?php echo $parsed_url['port'] ?? '5432'; ?>
Database: <?php echo ltrim($parsed_url['path'], '/'); ?>
Username: <?php echo $parsed_url['user']; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="warning">
                <strong>⚠️ Security Notice:</strong> 
                Keep your database credentials secure. Never share them publicly or commit them to version control. 
                The database admin panel is for development/testing purposes only.
            </div>

            <div class="links">
                <a href="database-admin.php">🗄️ Database Admin Panel</a>
                <a href="admin" class="secondary">🛡️ Admin Panel</a>
                <a href="index.php" class="secondary">🏠 Homepage</a>
            </div>
        </div>
    </div>
</body>
</html>
