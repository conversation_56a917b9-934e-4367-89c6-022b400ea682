services:
  # Web Application (PHP + Apache)
  web:
    build: .
    container_name: event-booking-web
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
    depends_on:
      - db
    environment:
      - DB_HOST=db
      - DB_USER=root
      - DB_PASS=rootpassword
      - DB_NAME=event_booking_system
    networks:
      - event-booking-network

  # MySQL Database
  db:
    image: mysql:8.0
    container_name: event-booking-db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: event_booking_system
      MYSQL_USER: appuser
      MYSQL_PASSWORD: apppassword

    ports:
      - "13306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
    networks:
      - event-booking-network

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: event-booking-phpmyadmin
    restart: always
    ports:
      - "8082:80"
    environment:
      PMA_HOST: db
      PMA_USER: root
      PMA_PASSWORD: rootpassword
    depends_on:
      - db
    networks:
      - event-booking-network

volumes:
  db_data:

networks:
  event-booking-network:
    driver: bridge
