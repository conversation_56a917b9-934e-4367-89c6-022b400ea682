<?php
/**
 * African Event Image Mapping
 * Maps African event types to authentic African images
 */

$africanEventImages = [
    // Music & Entertainment
    'afrobeats' => 'images/events/african/music/african_afrobeats_festival.jpg',
    'makossa' => 'images/events/african/music/african_makossa_concert.jpg',
    'jazz' => 'images/events/african/music/african_jazz_night.jpg',
    'traditional_music' => 'images/events/african/music/african_traditional_drums.jpg',
    'music_festival' => 'images/events/african/music/african_music_festival.jpg',
    'concert' => 'images/events/african/music/african_concert_stage.jpg',
    
    // Sports
    'football' => 'images/events/african/sports/african_football_stadium.jpg',
    'boxing' => 'images/events/african/sports/african_boxing_match.jpg',
    'athletics' => 'images/events/african/sports/african_athletics_track.jpg',
    'handball' => 'images/events/african/sports/african_handball_court.jpg',
    'basketball' => 'images/events/african/sports/african_basketball_arena.jpg',
    
    // Culture & Heritage
    'cultural_festival' => 'images/events/african/culture/african_cultural_dance.jpg',
    'traditional_ceremony' => 'images/events/african/culture/african_traditional_ceremony.jpg',
    'art_exhibition' => 'images/events/african/culture/african_art_gallery.jpg',
    'heritage' => 'images/events/african/culture/african_heritage_site.jpg',
    'dance' => 'images/events/african/culture/african_traditional_dance.jpg',
    
    // Business & Tech
    'tech_conference' => 'images/events/african/tech/african_tech_summit.jpg',
    'business_meeting' => 'images/events/african/business/african_business_conference.jpg',
    'startup_pitch' => 'images/events/african/business/african_startup_event.jpg',
    'workshop' => 'images/events/african/business/african_workshop.jpg',
    'summit' => 'images/events/african/business/african_summit.jpg',
    
    // Food & Dining
    'food_festival' => 'images/events/african/food/african_food_market.jpg',
    'wine_tasting' => 'images/events/african/food/african_wine_event.jpg',
    'cooking' => 'images/events/african/food/african_cooking_class.jpg',
    'restaurant' => 'images/events/african/food/african_restaurant.jpg',
    
    // Fashion & Style
    'fashion_show' => 'images/events/african/fashion/african_fashion_runway.jpg',
    'fashion_week' => 'images/events/african/fashion/african_fashion_week.jpg',
    'style_event' => 'images/events/african/fashion/african_style_showcase.jpg',
    
    // Entertainment
    'comedy' => 'images/events/african/entertainment/african_comedy_show.jpg',
    'theater' => 'images/events/african/entertainment/african_theater.jpg',
    'gaming' => 'images/events/african/entertainment/african_gaming_tournament.jpg',
    'party' => 'images/events/african/entertainment/african_party_event.jpg',
    
    // Venues (Cameroon specific)
    'yaunde' => 'images/events/african/venues/yaunde_venue.jpg',
    'douala' => 'images/events/african/venues/douala_venue.jpg',
    'palais_sports' => 'images/events/african/venues/palais_des_sports.jpg',
    'omnisport' => 'images/events/african/venues/omnisport_stadium.jpg',
    'hilton' => 'images/events/african/venues/hilton_yaunde.jpg',
    
    // Default
    'default' => 'images/events/african/african_event_default.jpg'
];

/**
 * Get African-themed image for event
 * @param string $eventName The name of the event
 * @return string Path to the appropriate African image
 */
function getAfricanEventImage($eventName) {
    global $africanEventImages;
    
    $eventName = strtolower($eventName);
    
    // Music events
    if (strpos($eventName, 'afrobeats') !== false || strpos($eventName, 'afro') !== false) {
        return $africanEventImages['afrobeats'];
    } elseif (strpos($eventName, 'makossa') !== false) {
        return $africanEventImages['makossa'];
    } elseif (strpos($eventName, 'jazz') !== false) {
        return $africanEventImages['jazz'];
    } elseif (strpos($eventName, 'music') !== false || strpos($eventName, 'festival') !== false) {
        return $africanEventImages['music_festival'];
    } elseif (strpos($eventName, 'concert') !== false) {
        return $africanEventImages['concert'];
    }
    
    // Sports events
    elseif (strpos($eventName, 'football') !== false || strpos($eventName, 'lions') !== false || strpos($eventName, 'derby') !== false) {
        return $africanEventImages['football'];
    } elseif (strpos($eventName, 'boxing') !== false || strpos($eventName, 'championship') !== false) {
        return $africanEventImages['boxing'];
    } elseif (strpos($eventName, 'handball') !== false) {
        return $africanEventImages['handball'];
    } elseif (strpos($eventName, 'basketball') !== false) {
        return $africanEventImages['basketball'];
    } elseif (strpos($eventName, 'athletics') !== false || strpos($eventName, 'track') !== false) {
        return $africanEventImages['athletics'];
    }
    
    // Cultural events
    elseif (strpos($eventName, 'cultural') !== false || strpos($eventName, 'heritage') !== false) {
        return $africanEventImages['cultural_festival'];
    } elseif (strpos($eventName, 'traditional') !== false || strpos($eventName, 'ceremony') !== false) {
        return $africanEventImages['traditional_ceremony'];
    } elseif (strpos($eventName, 'art') !== false || strpos($eventName, 'exhibition') !== false) {
        return $africanEventImages['art_exhibition'];
    } elseif (strpos($eventName, 'dance') !== false || strpos($eventName, 'dancing') !== false) {
        return $africanEventImages['dance'];
    }
    
    // Business & Tech
    elseif (strpos($eventName, 'tech') !== false || strpos($eventName, 'innovation') !== false) {
        return $africanEventImages['tech_conference'];
    } elseif (strpos($eventName, 'business') !== false || strpos($eventName, 'entrepreneur') !== false) {
        return $africanEventImages['business_meeting'];
    } elseif (strpos($eventName, 'startup') !== false || strpos($eventName, 'pitch') !== false) {
        return $africanEventImages['startup_pitch'];
    } elseif (strpos($eventName, 'workshop') !== false || strpos($eventName, 'training') !== false) {
        return $africanEventImages['workshop'];
    } elseif (strpos($eventName, 'summit') !== false || strpos($eventName, 'conference') !== false) {
        return $africanEventImages['summit'];
    }
    
    // Food & Dining
    elseif (strpos($eventName, 'food') !== false || strpos($eventName, 'culinary') !== false) {
        return $africanEventImages['food_festival'];
    } elseif (strpos($eventName, 'wine') !== false || strpos($eventName, 'tasting') !== false) {
        return $africanEventImages['wine_tasting'];
    } elseif (strpos($eventName, 'cooking') !== false || strpos($eventName, 'chef') !== false) {
        return $africanEventImages['cooking'];
    }
    
    // Fashion & Style
    elseif (strpos($eventName, 'fashion') !== false || strpos($eventName, 'runway') !== false) {
        return $africanEventImages['fashion_show'];
    } elseif (strpos($eventName, 'style') !== false || strpos($eventName, 'designer') !== false) {
        return $africanEventImages['style_event'];
    }
    
    // Entertainment
    elseif (strpos($eventName, 'comedy') !== false || strpos($eventName, 'comedian') !== false) {
        return $africanEventImages['comedy'];
    } elseif (strpos($eventName, 'theater') !== false || strpos($eventName, 'drama') !== false) {
        return $africanEventImages['theater'];
    } elseif (strpos($eventName, 'gaming') !== false || strpos($eventName, 'esports') !== false) {
        return $africanEventImages['gaming'];
    } elseif (strpos($eventName, 'party') !== false || strpos($eventName, 'celebration') !== false) {
        return $africanEventImages['party'];
    }
    
    // Venue-specific (Cameroon)
    elseif (strpos($eventName, 'yaunde') !== false || strpos($eventName, 'yaoundé') !== false) {
        return $africanEventImages['yaunde'];
    } elseif (strpos($eventName, 'douala') !== false) {
        return $africanEventImages['douala'];
    } elseif (strpos($eventName, 'palais') !== false) {
        return $africanEventImages['palais_sports'];
    } elseif (strpos($eventName, 'omnisport') !== false) {
        return $africanEventImages['omnisport'];
    } elseif (strpos($eventName, 'hilton') !== false) {
        return $africanEventImages['hilton'];
    }
    
    // Default African image
    else {
        return $africanEventImages['default'];
    }
}

/**
 * Get all available African image categories
 * @return array List of image categories
 */
function getAfricanImageCategories() {
    return [
        'music' => '🎵 Music & Entertainment',
        'sports' => '⚽ Sports',
        'culture' => '🎭 Culture & Heritage',
        'business' => '💼 Business & Tech',
        'food' => '🍽️ Food & Dining',
        'fashion' => '👗 Fashion & Style',
        'entertainment' => '🎪 Entertainment',
        'venues' => '🏢 Venues'
    ];
}

/**
 * Check if African image exists, fallback to default
 * @param string $imagePath Path to check
 * @return string Valid image path
 */
function validateAfricanImage($imagePath) {
    if (file_exists($imagePath)) {
        return $imagePath;
    }
    
    // Fallback to default African image
    $defaultPath = 'images/events/african/african_event_default.jpg';
    if (file_exists($defaultPath)) {
        return $defaultPath;
    }
    
    // Final fallback to original default
    return 'images/events/default-event.jpg';
}

/**
 * Get random African image for variety
 * @param string $category Image category
 * @return string Random image path from category
 */
function getRandomAfricanImage($category = 'default') {
    global $africanEventImages;
    
    $categoryImages = array_filter($africanEventImages, function($key) use ($category) {
        return strpos($key, $category) !== false;
    }, ARRAY_FILTER_USE_KEY);
    
    if (!empty($categoryImages)) {
        $randomKey = array_rand($categoryImages);
        return $categoryImages[$randomKey];
    }
    
    return $africanEventImages['default'];
}
?>
