<?php
/**
 * Add Exciting Sports and Entertainment Events
 * Adds Boxing, Football, Handball and other interesting events
 */

require_once 'config/database.php';

echo "<h1>🏆 Adding Exciting Sports & Entertainment Events</h1>";
echo "<p>Adding Boxing, Football, Handball, and other thrilling events to your system!</p>";

try {
    $pdo = getDBConnection();
    
    // First, let's download images for sports events
    echo "<h2>📥 Downloading Sports Event Images</h2>";
    
    $sportsImages = [
        'boxing-match' => [
            'url' => 'https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'boxing-match.jpg',
            'description' => 'Professional boxing ring with dramatic lighting'
        ],
        'football-stadium' => [
            'url' => 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'football-stadium.jpg',
            'description' => 'Football stadium with green field and crowd'
        ],
        'handball-court' => [
            'url' => 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'handball-court.jpg',
            'description' => 'Indoor handball court with players in action'
        ],
        'basketball-arena' => [
            'url' => 'https://images.unsplash.com/photo-1546519638-68e109498ffc?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'basketball-arena.jpg',
            'description' => 'Basketball arena with court and spectator seats'
        ],
        'comedy-show' => [
            'url' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'comedy-show.jpg',
            'description' => 'Comedy club stage with microphone and spotlight'
        ],
        'fashion-show' => [
            'url' => 'https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'fashion-show.jpg',
            'description' => 'Fashion runway with models and audience'
        ],
        'gaming-tournament' => [
            'url' => 'https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'gaming-tournament.jpg',
            'description' => 'Esports gaming tournament with multiple screens'
        ],
        'dance-competition' => [
            'url' => 'https://images.unsplash.com/photo-1508700115892-45ecd05ae2ad?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=90',
            'filename' => 'dance-competition.jpg',
            'description' => 'Dance floor with performers and colorful lights'
        ]
    ];
    
    // Create sports images directory
    $sportsImageDir = 'images/events/sports/';
    if (!is_dir($sportsImageDir)) {
        mkdir($sportsImageDir, 0777, true);
        echo "<p>✅ Created sports images directory: $sportsImageDir</p>";
    }
    
    // Download sports images
    foreach ($sportsImages as $key => $imageData) {
        $filepath = $sportsImageDir . $imageData['filename'];
        
        if (!file_exists($filepath)) {
            $imageContent = @file_get_contents($imageData['url']);
            if ($imageContent !== false) {
                if (file_put_contents($filepath, $imageContent)) {
                    $fileSize = round(filesize($filepath) / 1024, 2);
                    echo "<p>✅ Downloaded: {$imageData['filename']} ($fileSize KB)</p>";
                } else {
                    echo "<p>❌ Failed to save: {$imageData['filename']}</p>";
                }
            } else {
                echo "<p>❌ Failed to download: {$imageData['filename']}</p>";
            }
        } else {
            echo "<p>⚠️ Already exists: {$imageData['filename']}</p>";
        }
        usleep(500000); // 0.5 second delay
    }
    
    echo "<h2>🏆 Adding Sports & Entertainment Events</h2>";
    
    // Define exciting events to add
    $newEvents = [
        [
            'name' => 'Cameroon Boxing Championship 2025',
            'description' => 'Professional boxing championship featuring the best fighters from Cameroon and Central Africa. Witness explosive matches, championship bouts, and rising stars in the boxing world.',
            'date' => '2025-07-15',
            'time' => '19:00:00',
            'venue' => 'Palais des Sports',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Boxing Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/boxing-match.jpg',
            'price' => 15000,
            'total_tickets' => 3000,
            'available_tickets' => 3000
        ],
        [
            'name' => 'Lions vs Eagles Football Derby',
            'description' => 'Epic football match between Cameroon\'s top teams. Experience the passion, skill, and excitement of professional football in a packed stadium atmosphere.',
            'date' => '2025-08-20',
            'time' => '16:00:00',
            'venue' => 'Ahmadou Ahidjo Stadium',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Football Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/football-stadium.jpg',
            'price' => 5000,
            'total_tickets' => 40000,
            'available_tickets' => 40000
        ],
        [
            'name' => 'Central Africa Handball Tournament',
            'description' => 'International handball tournament featuring teams from across Central Africa. Fast-paced action, skilled athletes, and competitive matches in a world-class facility.',
            'date' => '2025-09-10',
            'time' => '14:00:00',
            'venue' => 'Multipurpose Sports Complex',
            'location' => 'Douala, Cameroon',
            'organizer' => 'Central Africa Handball Association',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/handball-court.jpg',
            'price' => 8000,
            'total_tickets' => 2500,
            'available_tickets' => 2500
        ],
        [
            'name' => 'Basketball All-Stars Cameroon',
            'description' => 'Annual basketball all-star game featuring the best players from Cameroon\'s professional leagues. High-flying dunks, three-point contests, and spectacular plays.',
            'date' => '2025-10-05',
            'time' => '18:30:00',
            'venue' => 'Basketball Arena Douala',
            'location' => 'Douala, Cameroon',
            'organizer' => 'Cameroon Basketball Federation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/basketball-arena.jpg',
            'price' => 12000,
            'total_tickets' => 5000,
            'available_tickets' => 5000
        ],
        [
            'name' => 'Comedy Night Cameroon 2025',
            'description' => 'Hilarious comedy show featuring Cameroon\'s funniest comedians and special international guests. An evening of laughter, entertainment, and unforgettable moments.',
            'date' => '2025-06-25',
            'time' => '20:00:00',
            'venue' => 'Hilton Yaoundé Conference Center',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Comedy Club',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/comedy-show.jpg',
            'price' => 10000,
            'total_tickets' => 800,
            'available_tickets' => 800
        ],
        [
            'name' => 'Cameroon Fashion Week 2025',
            'description' => 'Premier fashion event showcasing the latest designs from Cameroon\'s top fashion designers. Runway shows, fashion exhibitions, and networking opportunities.',
            'date' => '2025-11-15',
            'time' => '19:30:00',
            'venue' => 'Douala Convention Center',
            'location' => 'Douala, Cameroon',
            'organizer' => 'Cameroon Fashion Council',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/fashion-show.jpg',
            'price' => 25000,
            'total_tickets' => 1200,
            'available_tickets' => 1200
        ],
        [
            'name' => 'Esports Gaming Championship',
            'description' => 'Major gaming tournament featuring popular esports titles. Professional gamers, live streaming, prizes, and the latest in gaming technology and entertainment.',
            'date' => '2025-12-08',
            'time' => '10:00:00',
            'venue' => 'Digital Innovation Hub',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'Cameroon Esports Association',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/gaming-tournament.jpg',
            'price' => 7500,
            'total_tickets' => 1500,
            'available_tickets' => 1500
        ],
        [
            'name' => 'African Dance Festival 2025',
            'description' => 'Spectacular dance festival celebrating African culture through traditional and contemporary dance performances. Multiple dance groups, workshops, and cultural exhibitions.',
            'date' => '2025-05-30',
            'time' => '17:00:00',
            'venue' => 'Cultural Center Yaoundé',
            'location' => 'Yaoundé, Cameroon',
            'organizer' => 'African Cultural Heritage Foundation',
            'organizer_contact' => '<EMAIL>',
            'image' => 'sports/dance-competition.jpg',
            'price' => 6000,
            'total_tickets' => 2000,
            'available_tickets' => 2000
        ]
    ];
    
    $successCount = 0;
    
    foreach ($newEvents as $event) {
        // Check if event already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM events WHERE name = ?");
        $stmt->execute([$event['name']]);
        
        if ($stmt->fetchColumn() == 0) {
            // Insert new event
            $stmt = $pdo->prepare("
                INSERT INTO events (name, description, date, time, venue, location, organizer, organizer_contact, image, price, total_tickets, available_tickets, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
            ");
            
            $stmt->execute([
                $event['name'],
                $event['description'],
                $event['date'],
                $event['time'],
                $event['venue'],
                $event['location'],
                $event['organizer'],
                $event['organizer_contact'],
                $event['image'],
                $event['price'],
                $event['total_tickets'],
                $event['available_tickets']
            ]);
            
            echo "<div style='margin: 10px 0; padding: 15px; border: 1px solid #28a745; border-radius: 8px; background: #d4edda;'>";
            echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ {$event['name']}</h4>";
            echo "<p style='margin: 5px 0;'><strong>Date:</strong> {$event['date']} at {$event['time']}</p>";
            echo "<p style='margin: 5px 0;'><strong>Venue:</strong> {$event['venue']}, {$event['location']}</p>";
            echo "<p style='margin: 5px 0;'><strong>Price:</strong> " . number_format($event['price']) . " CFA</p>";
            echo "<p style='margin: 5px 0;'><strong>Capacity:</strong> " . number_format($event['total_tickets']) . " tickets</p>";
            echo "</div>";
            
            $successCount++;
        } else {
            echo "<div style='margin: 10px 0; padding: 15px; border: 1px solid #ffc107; border-radius: 8px; background: #fff3cd;'>";
            echo "<h4 style='color: #856404; margin: 0;'>⚠️ {$event['name']} - Already exists</h4>";
            echo "</div>";
        }
    }
    
    echo "<h2>📊 Summary</h2>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<p><strong>Total Events Added:</strong> $successCount</p>";
    echo "<p><strong>Event Categories:</strong></p>";
    echo "<ul>";
    echo "<li>🥊 <strong>Boxing:</strong> Professional championship matches</li>";
    echo "<li>⚽ <strong>Football:</strong> Derby matches and tournaments</li>";
    echo "<li>🤾 <strong>Handball:</strong> International tournaments</li>";
    echo "<li>🏀 <strong>Basketball:</strong> All-star games and competitions</li>";
    echo "<li>😂 <strong>Comedy:</strong> Stand-up comedy shows</li>";
    echo "<li>👗 <strong>Fashion:</strong> Fashion weeks and runway shows</li>";
    echo "<li>🎮 <strong>Gaming:</strong> Esports tournaments</li>";
    echo "<li>💃 <strong>Dance:</strong> Cultural dance festivals</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 20px; border: 1px solid red; border-radius: 8px; background: #ffe6e6;'>";
    echo "<h3>❌ Error occurred:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>🎉 Sports & Entertainment Events Added Successfully!</h2>";
echo "<p>Your Event Booking System now features exciting sports and entertainment events!</p>";
echo "<p><a href='events.php' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: bold;'>🏆 View All Events</a></p>";
?>
