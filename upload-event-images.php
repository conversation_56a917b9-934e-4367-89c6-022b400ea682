<?php
/**
 * Event Image Upload Tool
 * Upload and manage event images manually
 */

echo "<h1>📸 Event Image Upload Tool</h1>";
echo "<p>Upload custom images for your events...</p>";

// Include database configuration
require_once 'config/database.php';

// Create uploads directory if it doesn't exist
$uploadDir = 'images/events/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

// Handle image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['event_image'])) {
    $eventId = $_POST['event_id'] ?? '';
    $file = $_FILES['event_image'];
    
    if ($eventId && $file['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!in_array($file['type'], $allowedTypes)) {
            $error = "Invalid file type. Please upload JPG, PNG, GIF, or WebP images.";
        } elseif ($file['size'] > $maxSize) {
            $error = "File too large. Maximum size is 5MB.";
        } else {
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'event_' . $eventId . '_' . time() . '.' . $extension;
            $uploadPath = $uploadDir . $filename;
            
            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                // Update database
                try {
                    $pdo = getDBConnection();
                    $stmt = $pdo->prepare("UPDATE events SET image = ? WHERE id = ?");
                    $stmt->execute(['images/events/' . $filename, $eventId]);
                    
                    $success = "Image uploaded successfully! Event updated.";
                } catch (Exception $e) {
                    $error = "Database error: " . $e->getMessage();
                }
            } else {
                $error = "Failed to upload image.";
            }
        }
    } else {
        $error = "Please select an event and upload a valid image.";
    }
}

// Get all events
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT id, name, image FROM events ORDER BY name");
    $events = $stmt->fetchAll();
} catch (Exception $e) {
    $events = [];
    $dbError = "Database connection failed: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Image Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .event-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .event-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .event-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .no-image {
            width: 100%;
            height: 150px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            margin-bottom: 10px;
            color: #666;
        }
        .links {
            text-align: center;
            margin-top: 30px;
        }
        .links a {
            display: inline-block;
            margin: 10px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📸 Event Image Upload Tool</h1>
        
        <?php if (isset($success)): ?>
            <div class="alert alert-success">✅ <?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error">❌ <?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (isset($dbError)): ?>
            <div class="alert alert-error">❌ <?php echo $dbError; ?></div>
        <?php else: ?>
            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="event_id">Select Event:</label>
                    <select name="event_id" id="event_id" required>
                        <option value="">Choose an event...</option>
                        <?php foreach ($events as $event): ?>
                            <option value="<?php echo $event['id']; ?>">
                                <?php echo htmlspecialchars($event['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="event_image">Upload Image:</label>
                    <input type="file" name="event_image" id="event_image" accept="image/*" required>
                    <small style="color: #666; display: block; margin-top: 5px;">
                        Supported formats: JPG, PNG, GIF, WebP (Max 5MB)
                    </small>
                </div>
                
                <button type="submit">📸 Upload Image</button>
            </form>
            
            <h2>📋 Current Events</h2>
            <div class="event-grid">
                <?php foreach ($events as $event): ?>
                    <div class="event-card">
                        <?php if ($event['image'] && file_exists($event['image'])): ?>
                            <img src="<?php echo $event['image']; ?>" alt="<?php echo htmlspecialchars($event['name']); ?>" class="event-image">
                        <?php else: ?>
                            <div class="no-image">No Image</div>
                        <?php endif; ?>
                        <h4><?php echo htmlspecialchars($event['name']); ?></h4>
                        <small>Event ID: <?php echo $event['id']; ?></small>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="links">
            <a href="index.php">🏠 Homepage</a>
            <a href="events.php">🎫 View Events</a>
            <a href="admin">🛡️ Admin Panel</a>
        </div>
    </div>
</body>
</html>
